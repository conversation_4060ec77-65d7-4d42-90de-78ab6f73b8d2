import { createContext, useContext, useEffect, useRef, useState } from "react"
import { AppState, type AppStateStatus, Platform } from "react-native"
import NetInfo from "@react-native-community/netinfo"
import useStorage from "@/src/hooks/storage"
import type { SocketContextProviderProps, SocketContextValue, MessageHandler } from "./types"
import { showToast } from "@/src/utilities/toast"

declare const __DEV__: boolean

const SocketContext = createContext<SocketContextValue | null>(null)

const getBaseUrl = (): string => {
  const isDevelopment = __DEV__
  if (isDevelopment) {
    const localhost = Platform.OS === "ios" ? "localhost" : "********"
    return `ws://${localhost}:4003`
  }
  return "wss://api.b2c.navicater.com"
}

interface WebSocketMessage {
  type: string
  data: Record<string, unknown>
  timestamp: number
  profileId?: string
}

const SocketContextProvider = ({ children }: SocketContextProviderProps) => {
  const [isConnected, setIsConnected] = useState<boolean>(false)
  const [isConnecting, setIsConnecting] = useState<boolean>(false)
  const [socket, setSocket] = useState<WebSocket | null>(null)
  const [profileId, setProfileId] = useState<string | null>(null)
  const [_connectionError, setConnectionError] = useState<string | null>(null)
  const { getStorage } = useStorage()

  const socketRef = useRef<WebSocket | null>(null)
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const mountedRef = useRef<boolean>(true)
  const retryCountRef = useRef<number>(0)
  const isConnectingRef = useRef<boolean>(false)
  const maxRetries = 5
  const messageHandlers = useRef<Map<string, Set<MessageHandler>>>(new Map())

  const startPingInterval = (): void => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current)
    }

    pingIntervalRef.current = setInterval(() => {
      if (socketRef.current?.readyState === WebSocket.OPEN) {
        try {
          const pingMessage: WebSocketMessage = {
            type: "ping",
            data: {},
            timestamp: Date.now(),
            profileId: profileId || undefined,
          }
          socketRef.current.send(JSON.stringify(pingMessage))
        } catch (error) {
          console.error("Ping failed:", error)
        }
      }
    }, 30000)
  }

  const stopPingInterval = (): void => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current)
      pingIntervalRef.current = null
    }
  }

  const clearTimeouts = (): void => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current)
      connectionTimeoutRef.current = null
    }
  }

  const resetStates = (): void => {
    setIsConnected(false)
    setIsConnecting(false)
    isConnectingRef.current = false
    setSocket(null)
    socketRef.current = null
    stopPingInterval()
    clearTimeouts()
  }

  const broadcastMessage = (type: string, data: Record<string, unknown>, rawMessage: Record<string, unknown>): void => {
    const handlers = messageHandlers.current.get(type)
    if (handlers && handlers.size > 0) {
      handlers.forEach((handler) => {
        try {
          handler(type, data, rawMessage)
        } catch (error) {
          console.error("Message handler failed:", error)
        }
      })
    }
  }

  const checkNetworkConnectivity = async (): Promise<boolean> => {
    try {
      const netInfo = await NetInfo.fetch()
      return netInfo.isConnected === true && netInfo.isInternetReachable !== false
    } catch (error) {
      console.error("Network check failed:", error)
      return false
    }
  }

  const scheduleReconnect = (): void => {
    if (retryCountRef.current >= maxRetries || !mountedRef.current) {
      console.log("Max retries reached or component unmounted")
      return
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }

    const delay = Math.min(30000, 1000 * Math.pow(2, retryCountRef.current))
    console.log(`Scheduling reconnect in ${delay}ms (attempt ${retryCountRef.current + 1}/${maxRetries})`)

    reconnectTimeoutRef.current = setTimeout(async () => {
      if (mountedRef.current && profileId && !isConnected && !isConnectingRef.current) {
        retryCountRef.current++
        await connect()
      }
    }, delay)
  }

  const connect = async (): Promise<void> => {
    if (!profileId || !mountedRef.current) {
      console.log("Cannot connect: missing profileId or component unmounted")
      return
    }

    if (isConnectingRef.current) {
      console.log("Already connecting, skipping")
      return
    }

    if (isConnected) {
      console.log("Already connected, skipping")
      return
    }

    const networkAvailable = await checkNetworkConnectivity()
    if (!networkAvailable) {
      console.log("Network not available, scheduling reconnect")
      setConnectionError("Network not available")
      scheduleReconnect()
      return
    }

    console.log("Starting connection attempt...")
    isConnectingRef.current = true
    setIsConnecting(true)
    setConnectionError(null)

    try {
      if (socketRef.current) {
        console.log("Disconnecting existing socket")
        socketRef.current.close()
        socketRef.current = null
      }

      const baseUrl = getBaseUrl()
      const wsUrl = `${baseUrl}/ws/chat/${profileId}`
      console.log(`Connecting to: ${wsUrl}`)

      const ws = new WebSocket(wsUrl, [], {
        headers: {
          'User-Agent': 'Navicater/1.0',
          'X-Platform': Platform.OS,
          'X-Device-Type': Platform.OS === 'ios' ? 'iOS' : 'Android',
          'X-Profile-ID': profileId
        }
      })
      socketRef.current = ws
      setSocket(ws)

      connectionTimeoutRef.current = setTimeout(() => {
        if (isConnectingRef.current && !isConnected) {
          console.error("Connection timeout after 15 seconds")
          setConnectionError("Connection timeout")
          ws.close()
          if (mountedRef.current) {
            isConnectingRef.current = false
            setIsConnecting(false)
            scheduleReconnect()
          }
        }
      }, 15000)

      ws.onopen = () => {
        if (!mountedRef.current) return

        console.log("✅ WebSocket connected successfully")
        clearTimeouts()
        setIsConnected(true)
        setIsConnecting(false)
        setConnectionError(null)
        isConnectingRef.current = false
        retryCountRef.current = 0
        startPingInterval()

        const connectMessage: WebSocketMessage = {
          type: "connect",
          data: { profileId },
          timestamp: Date.now(),
          profileId,
        }
        ws.send(JSON.stringify(connectMessage))
      }

      ws.onclose = (event) => {
        if (!mountedRef.current) return

        console.log("❌ WebSocket disconnected:", event.code, event.reason)
        clearTimeouts()
        setIsConnected(false)
        setIsConnecting(false)
        isConnectingRef.current = false
        stopPingInterval()

        if (event.code !== 1000 && socketRef.current === ws) {
          setConnectionError(`Disconnected: ${event.reason || event.code}`)
          scheduleReconnect()
        }
      }

      ws.onerror = (error) => {
        showToast({type:"error", message: String(error)})
        if (!mountedRef.current) return

        console.error("🔴 WebSocket connection error:", error)
        setConnectionError("WebSocket connection error")
        clearTimeouts()
        setIsConnecting(false)
        isConnectingRef.current = false

        if (socketRef.current === ws) {
          scheduleReconnect()
        }
      }

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)

          if (message.type === "pong") {
            broadcastMessage("pong", message.data, { type: "pong", data: message.data })
            return
          }

          if (message.type === "ping") {
            const pongMessage: WebSocketMessage = {
              type: "pong",
              data: {},
              timestamp: Date.now(),
              profileId: profileId || undefined,
            }
            ws.send(JSON.stringify(pongMessage))
            broadcastMessage("ping", message.data, { type: "ping", data: message.data })
            return
          }

          if (message.type === "user_joined") {
            console.log("User joined:", message.data)
            broadcastMessage("user_joined", message.data, { type: "user_joined", data: message.data })
            return
          }

          if (message.type === "user_left") {
            console.log("User left:", message.data)
            broadcastMessage("user_left", message.data, { type: "user_left", data: message.data })
            return
          }

          console.log(`WebSocket event: ${message.type}`, message.data)
          broadcastMessage(message.type, message.data, { type: message.type, data: message.data })
        } catch (error) {
          console.error("Failed to parse WebSocket message:", error)
        }
      }
    } catch (error) {
      if (mountedRef.current) {
        console.error("Connection creation error:", error)
        setConnectionError(error instanceof Error ? error.message : "Connection failed")
        setIsConnecting(false)
        isConnectingRef.current = false
        scheduleReconnect()
      }
    }
  }

  const connectSocket = async (newProfileId: string): Promise<void> => {
    if (!newProfileId) {
      throw new Error("ProfileId is required")
    }

    console.log(`Connecting socket with profileId: ${newProfileId}`)

    if (socketRef.current) {
      socketRef.current.close()
    }

    resetStates()
    setProfileId(newProfileId)
    retryCountRef.current = 0
  }

  const disconnect = (permanent = false): void => {
    console.log(`Disconnecting socket (permanent: ${permanent})`)

    if (socketRef.current) {
      socketRef.current.close(1000, "Client disconnect")
    }

    resetStates()
    clearTimeouts()

    if (permanent) {
      setProfileId(null)
      retryCountRef.current = maxRetries
    }
  }

  const sendMessage = (type: string, data: Record<string, unknown>): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (socketRef.current?.readyState === WebSocket.OPEN) {
        try {
          console.log(`Sending message: ${type}`, data)
          const message: WebSocketMessage = {
            type,
            data,
            timestamp: Date.now(),
            profileId: profileId || undefined,
          }
          socketRef.current.send(JSON.stringify(message))
          resolve()
        } catch (error) {
          console.error("Send message error:", error)
          reject(error)
        }
      } else {
        const error = new Error("WebSocket not connected")
        console.error("Cannot send message:", {
          isConnected,
          hasSocket: !!socketRef.current,
          readyState: socketRef.current?.readyState,
        })
        reject(error)
      }
    })
  }

  const subscribeToMessages = (messageTypes: string[], handler: MessageHandler): (() => void) => {
    messageTypes.forEach((type) => {
      if (!messageHandlers.current.has(type)) {
        messageHandlers.current.set(type, new Set())
      }
      messageHandlers.current.get(type)!.add(handler)
    })

    return () => {
      messageTypes.forEach((type) => {
        const handlers = messageHandlers.current.get(type)
        if (handlers) {
          handlers.delete(handler)
          if (handlers.size === 0) {
            messageHandlers.current.delete(type)
          }
        }
      })
    }
  }

  useEffect(() => {
    mountedRef.current = true

    return () => {
      mountedRef.current = false
      resetStates()
      clearTimeouts()
    }
  }, [])

  useEffect(() => {
    const loadProfileId = async (): Promise<void> => {
      try {
        const storedProfileId = await getStorage("userProfileId")
        if (storedProfileId && mountedRef.current) {
          console.log("Loaded profileId from storage:", storedProfileId)
          setProfileId(storedProfileId)
        }
      } catch (error) {
        console.error("Failed to load profile ID:", error)
      }
    }

    loadProfileId()
  }, [getStorage])

  useEffect(() => {
    if (profileId && !isConnected && !isConnectingRef.current && mountedRef.current) {
      console.log("ProfileId available, starting connection...")
      const timeoutId = setTimeout(() => {
        if (mountedRef.current && !isConnected && !isConnectingRef.current) {
          connect()
        }
      }, 100)

      return () => clearTimeout(timeoutId)
    }
  }, [profileId, isConnected])

  useEffect(() => {
    const netInfoUnsubscribe = NetInfo.addEventListener((state) => {
      console.log("Network state changed:", state)
      if (
        state.isConnected &&
        state.isInternetReachable !== false &&
        profileId &&
        !isConnected &&
        !isConnectingRef.current &&
        mountedRef.current
      ) {
        console.log("Network available, resetting retry count and connecting...")
        retryCountRef.current = 0
        setTimeout(() => {
          if (mountedRef.current && !isConnected && !isConnectingRef.current) {
            connect()
          }
        }, 1000)
      }
    })

    const appStateSubscription = AppState.addEventListener("change", (nextAppState: AppStateStatus) => {
      console.log("App state changed:", nextAppState)
      if (nextAppState === "active" && profileId && !isConnected && !isConnectingRef.current && mountedRef.current) {
        console.log("App became active, attempting to connect...")
        setTimeout(() => {
          if (mountedRef.current && !isConnected && !isConnectingRef.current) {
            connect()
          }
        }, 500)
      }
    })

    return () => {
      netInfoUnsubscribe()
      appStateSubscription?.remove()
    }
  }, [profileId, isConnected])

  const value: SocketContextValue = {
    isConnected,
    isConnecting,
    socket,
    sendMessage,
    disconnect,
    subscribeToMessages,
    connectSocket,
  }

  return <SocketContext.Provider value={value}>{children}</SocketContext.Provider>
}

export const useSocket = (): SocketContextValue => {
  const context = useContext(SocketContext)
  if (!context) {
    throw new Error("useSocket must be used within a SocketContextProvider")
  }
  return context
}

export default SocketContextProvider
