import { useRef } from 'react';
import { Pressable, View, Animated } from 'react-native';
import {
  type BottomTabNavigationProp,
  createBottomTabNavigator,
  type BottomTabBarButtonProps,
} from '@react-navigation/bottom-tabs';
import type { EventArg, ParamListBase } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import CreateStackNavigator from '@/src/navigation/stacks/CreateStack';
import HomeStackNavigator from '@/src/navigation/stacks/HomeStack';
import NotificationStackNavigator from '@/src/navigation/stacks/NotificationStack';
import ProfileStackNavigator from '@/src/navigation/stacks/ProfileStack';
import AnalysisIcon from '@/src/assets/svgs/AnalysisTab';
import HomeIcon from '@/src/assets/svgs/HomeTab';
import LearnAndCollabIcon from '@/src/assets/svgs/LearnCollabTab';
import NotificationIcon from '@/src/assets/svgs/NotificationTab';
import LearnCollabStackNavigator from '../stacks/LearnCollabStack';
import type {
  CreateButtonProps,
  TabBarIconProps,
  TabIconProps,
  HomeScreenActionsRef,
} from '../types';

const Tab = createBottomTabNavigator();

const TabPressable = (props: BottomTabBarButtonProps) => {
  const animatedValue = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(animatedValue, { toValue: 0.9, useNativeDriver: true }).start();
  };

  const handlePressOut = () => {
    Animated.spring(animatedValue, {
      toValue: 1,
      friction: 5,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  const { children, onPress, style, accessibilityRole } = props;

  return (
    <Pressable
      onPress={onPress}
      style={style}
      android_ripple={{ color: 'transparent' }}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      delayLongPress={250}
      accessibilityRole={accessibilityRole}
      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
    >
      <Animated.View style={{ transform: [{ scale: animatedValue }] }}>{children}</Animated.View>
    </Pressable>
  );
};

const profileStackListener = ({
  navigation,
}: {
  navigation: BottomTabNavigationProp<ParamListBase>;
}) => ({
  tabPress: (e: EventArg<'tabPress', true>) => {
    e.preventDefault();
    navigation.reset({
      index: 0,
      routes: [
        {
          name: 'ProfileStack',
          params: {
            screen: 'UserProfile',
            params: { fromTabPress: true },
          },
        },
      ],
    });
  },
});

const createStackListener = ({
  navigation,
}: {
  navigation: BottomTabNavigationProp<ParamListBase>;
}) => ({
  tabPress: (e: EventArg<'tabPress', true>) => {
    e.preventDefault();
    navigation.navigate('CreateStack', {
      screen: 'CreateContent',
      params: {
        type: 'USER_POST',
        editing: undefined,
        portUnLocode: undefined,
        postId: undefined,
      },
    });
  },
});

const TabIcon = ({ IconComponent, focused, tabName }: TabIconProps) => {
  const shouldApplyBackground = focused && tabName !== 'ProfileStack';
  const fill = focused ? '#006400' : '#555555';

  return (
    <View
      className={`items-center justify-center px-5 py-4 rounded-3xl ${shouldApplyBackground ? 'bg-[#DDEFC8]' : ''}`}
      pointerEvents="none"
    >
      <IconComponent fill={fill} />
    </View>
  );
};

const CreateButton = ({ focused }: CreateButtonProps) => {
  return (
    <View className="items-center justify-center -top-[15px]" pointerEvents="none">
      <View className="w-[60px] h-[60px] rounded-full bg-white items-center justify-center shadow-lg">
        <View
          className={`w-[50px] h-[50px] rounded-full items-center justify-center border-2 border-[#006400] ${focused ? 'bg-[#006400]' : 'bg-[#DDEFC8]'}`}
        >
          <AnalysisIcon fill={focused ? '#FFFFFF' : '#006400'} />
        </View>
      </View>
    </View>
  );
};

const ProfileTabIcon = ({ focused }: { focused: boolean }) => {
  const currentUser = useSelector(selectCurrentUser);

  return (
    <View
      className={`rounded-full p-[2px] ${focused ? 'border-2 border-[#006400]' : ''}`}
      pointerEvents="none"
    >
      <UserAvatar
        avatarUri={currentUser?.avatar}
        name={currentUser?.fullName}
        width={38}
        height={38}
        className="shadow-sm"
        placeholderClassName="shadow-md"
      />
    </View>
  );
};

const renderHomeTabIcon = (props: TabBarIconProps) => (
  <TabIcon IconComponent={HomeIcon} focused={props.focused} tabName="HomeStack" />
);

const renderLearnCollabTabIcon = (props: TabBarIconProps) => (
  <TabIcon IconComponent={LearnAndCollabIcon} focused={props.focused} tabName="LearnCollabStack" />
);

const renderCreateTabIcon = (props: TabBarIconProps) => <CreateButton focused={props.focused} />;

const renderNotificationTabIcon = (props: TabBarIconProps) => (
  <TabIcon IconComponent={NotificationIcon} focused={props.focused} tabName="NotificationStack" />
);

const BottomTabNavigator = () => {
  const homeScreenActionsRef = useRef<HomeScreenActionsRef['current']>({});

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarShowLabel: false,
        tabBarStyle: {
          backgroundColor: '#fff',
          height: 85,
          borderTopWidth: 0,
          paddingHorizontal: 15,
          paddingBottom: 20,
          paddingTop: 20,
          shadowColor: '#333333',
          shadowOffset: {
            width: 0,
            height: -2,
          },
          shadowOpacity: 0.08,
          shadowRadius: 8,
          elevation: 10,
        },
        animation: 'shift',
        tabBarButton: TabPressable,
      }}
    >
      <Tab.Screen
        name="HomeStack"
        options={{
          tabBarIcon: renderHomeTabIcon,
        }}
        listeners={{
          tabPress: (e) => {
            homeScreenActionsRef.current.scrollToTop?.();
            homeScreenActionsRef.current.handleRefresh?.();
          },
        }}
      >
        {() => <HomeStackNavigator homeScreenActionsRef={homeScreenActionsRef} />}
      </Tab.Screen>
        <Tab.Screen
        name="LearnCollabStack"
        component={LearnCollabStackNavigator}
        options={{
          tabBarIcon: renderLearnCollabTabIcon,
          popToTopOnBlur: true,
        }}
      />
      <Tab.Screen
        name="CreateStack"
        component={CreateStackNavigator}
        options={{
          tabBarIcon: renderCreateTabIcon,
          popToTopOnBlur: true,
        }}
        listeners={createStackListener}
      />
      <Tab.Screen
        name="NotificationStack"
        component={NotificationStackNavigator}
        options={{
          tabBarIcon: renderNotificationTabIcon,
          popToTopOnBlur: true,
        }}
      />
      <Tab.Screen
        name="ProfileStack"
        component={ProfileStackNavigator}
        options={{ tabBarIcon: ProfileTabIcon }}
        listeners={profileStackListener}
      />
    </Tab.Navigator>
  );
};

export default BottomTabNavigator;
