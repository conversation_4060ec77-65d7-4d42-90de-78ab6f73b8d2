/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useEffect, useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import useStorage from '@/src/hooks/storage';
import { AuthStackParamListI } from '../../types';
import { screens } from './screen';

const AuthStack = createStackNavigator<AuthStackParamListI>();

const AuthStackNavigator = (): React.JSX.Element => {
  const { hasVisitedOnboarding } = useStorage();
  const [isReady, setIsReady] = useState(false);
  const [initialRoute, setInitialRoute] = useState<keyof AuthStackParamListI>('UserLogin');

  const currentUser = useSelector(selectCurrentUser);

  useEffect(() => {
    const determineInitialRoute = async () => {
      const visited = await hasVisitedOnboarding();
      if (!visited) {
        setInitialRoute('Onboarding');
      } else {
        const { token, isUsernameSaved, isPersonalDetailsSaved, isWorkDetailsSaved } = currentUser;
        if (token && !isUsernameSaved) {
          setInitialRoute('SetUsername');
        } else if (token && isUsernameSaved && (!isPersonalDetailsSaved || !isWorkDetailsSaved)) {
          setInitialRoute('AddUserDetailScreen');
        } else {
          setInitialRoute('UserLogin');
        }
      }
      setIsReady(true);
    };

    determineInitialRoute();
  }, [currentUser]);

  if (!isReady) {
    return <></>;
  }

  return (
    <AuthStack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'none',
        cardStyle: { backgroundColor: 'white' },
        cardOverlayEnabled: false,
        cardShadowEnabled: false,
      }}
      initialRouteName={initialRoute}
    >
      {screens.map(({ name, component }) => (
        <AuthStack.Screen key={name} name={name} component={component} />
      ))}
    </AuthStack.Navigator>
  );
};

export default AuthStackNavigator;
