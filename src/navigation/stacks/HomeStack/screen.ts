/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import type { HomeStackParamListI, StackScreenI } from '@/src/navigation/types';
import AIChatScreen from '@/src/screens/AIChat';
import ChatScreen from '@/src/screens/Chat';
import ChatsScreen from '@/src/screens/Chats';
import CommentScreen from '@/src/screens/Comment';
import ConnectionScreen from '@/src/screens/Connection';
import GlobalSearchScreen from '@/src/screens/GlobalSearch';
import HomeScreen from '@/src/screens/Home';
import LikesScreen from '@/src/screens/Likes';
import NotFoundScreen from '@/src/screens/NotFound';
import PortProfileScreen from '@/src/screens/PortProfile';
import ShipProfileScreen from '@/src/screens/ShipProfile';
import OtherUserProfileScreen from '@/src/screens/UserProfile';
import { withErrorBoundary } from '../../../hocs/withErrorBoundary';
import { withOfflineIndicator } from '../../../hocs/withOfflineIndicator';

const HomeScreenWithErrorBoundary = withErrorBoundary(HomeScreen, {
  title: 'Home Feed Error',
  subtitle: 'Something went wrong loading the home feed. Please try again.',
});

const CommentScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(CommentScreen, {
    title: 'Comments Error',
    subtitle: 'Something went wrong loading comments. Please try again.',
  }),
);

const LikesScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(LikesScreen, {
    title: 'Likes Error',
    subtitle: 'Something went wrong loading likes. Please try again.',
  }),
);

const ShipProfileScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(ShipProfileScreen, {
    title: 'Ship Profile Error',
    subtitle: 'Something went wrong loading the ship profile. Please try again.',
  }),
);

const PortProfileScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(PortProfileScreen, {
    title: 'Port Profile Error',
    subtitle: 'Something went wrong loading the port profile. Please try again.',
  }),
);

const OtherUserProfileScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(OtherUserProfileScreen, {
    title: 'User Profile Error',
    subtitle: 'Something went wrong loading the user profile. Please try again.',
  }),
);

const ConnectionScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(ConnectionScreen, {
    title: 'Connections Error',
    subtitle: 'Something went wrong loading connections. Please try again.',
  }),
);

const ChatsScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(ChatsScreen, {
    title: 'Chats Error',
    subtitle: 'Something went wrong loading your chats. Please try again.',
  }),
);

const ChatScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(ChatScreen, {
    title: 'Chat Error',
    subtitle: 'Something went wrong in the chat. Please try again.',
  }),
);

const GlobalSearchScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(GlobalSearchScreen, {
    title: 'Search Error',
    subtitle: 'Something went wrong during search. Please try again.',
  }),
);

const NotFoundScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(NotFoundScreen, {
    title: 'Page Not Found Error',
    subtitle: 'Something went wrong loading this page. Please try again.',
  }),
);

const AIChatScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(AIChatScreen, {
    title: 'AI Chat Error',
    subtitle: 'Something went wrong with the AI chat. Please try again.',
  }),
);

export const screens: StackScreenI<HomeStackParamListI>[] = [
  { name: 'Home', component: HomeScreenWithErrorBoundary },
  { name: 'Comment', component: CommentScreenWithErrorBoundary },
  { name: 'Likes', component: LikesScreenWithErrorBoundary },
  { name: 'ShipProfile', component: ShipProfileScreenWithErrorBoundary },
  { name: 'PortProfile', component: PortProfileScreenWithErrorBoundary },
  { name: 'OtherUserProfile', component: OtherUserProfileScreenWithErrorBoundary },
  { name: 'Connection', component: ConnectionScreenWithErrorBoundary },
  { name: 'Chats', component: ChatsScreenWithErrorBoundary },
  { name: 'Chat', component: ChatScreenWithErrorBoundary },
  { name: 'GlobalSearch', component: GlobalSearchScreenWithErrorBoundary },
  { name: 'NotFound', component: NotFoundScreenWithErrorBoundary },
  { name: 'AIChat', component: AIChatScreenWithErrorBoundary },
];
