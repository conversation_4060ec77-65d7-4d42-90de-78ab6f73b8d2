import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import type { HomeStackParamListI, HomeScreenActionsRef } from '@/src/navigation/types';
import { screens } from './screen';

const HomeStack = createStackNavigator<HomeStackParamListI>();

type HomeRouteNames = keyof HomeStackParamListI;

type HomeStackNavigatorProps = {
  initialRouteName?: HomeRouteNames;
  homeScreenActionsRef?: HomeScreenActionsRef;
};

const HomeStackNavigator = ({
  initialRouteName = 'Home',
  homeScreenActionsRef,
}: HomeStackNavigatorProps) => (
  <HomeStack.Navigator
    screenOptions={{
      headerShown: false,
      animation: 'none',
      cardStyle: { backgroundColor: 'white' },
      cardOverlayEnabled: false,
      cardShadowEnabled: false,
    }}
    initialRouteName={initialRouteName}
  >
    {screens.map(({ name, component }) =>
      name === 'Home' ? (
        <HomeStack.Screen
          key={name}
          name={name}
          children={(props) =>
            React.createElement(component as React.ComponentType<any>, {
              ...props,
              homeScreenActionsRef,
            })
          }
        />
      ) : (
        <HomeStack.Screen key={name} name={name} component={component} />
      ),
    )}
  </HomeStack.Navigator>
);

export default HomeStackNavigator;
