/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';
import type { QuestionStateI } from '../slices/question/types';

export const selectQuestionState = (state: RootState): QuestionStateI => state.question;

export const selectQuestionFormData = createSelector(
  [selectQuestionState],
  (questionState) => questionState.formData
);

export const selectQuestionType = createSelector(
  [selectQuestionFormData],
  (formData) => formData.type
);

export const selectQuestionTitle = createSelector(
  [selectQuestionFormData],
  (formData) => formData.title
);

export const selectQuestionDescription = createSelector(
  [selectQuestionFormData],
  (formData) => formData.description
);

export const selectQuestionTopics = createSelector(
  [selectQuestionFormData],
  (formData) => formData.topics
);

export const selectQuestionEquipmentCategory = createSelector(
  [selectQuestionFormData],
  (formData) => formData.equipmentCategory
);

export const selectQuestionEquipmentModel = createSelector(
  [selectQuestionFormData],
  (formData) => formData.equipmentModel
);

export const selectQuestionEquipmentManufacturer = createSelector(
  [selectQuestionFormData],
  (formData) => formData.equipmentManufacturer
);

export const selectQuestionDepartment = createSelector(
  [selectQuestionFormData],
  (formData) => formData.department
);

export const selectQuestionIsAnonymous = createSelector(
  [selectQuestionFormData],
  (formData) => formData.isAnonymous
);

export const selectQuestionCommunityId = createSelector(
  [selectQuestionFormData],
  (formData) => formData.communityId
);

export const selectQuestionFiles = createSelector(
  [selectQuestionFormData],
  (formData) => formData.files
);

export const selectQuestionLoading = createSelector(
  [selectQuestionState],
  (questionState) => questionState.loading
);

export const selectQuestionError = createSelector(
  [selectQuestionState],
  (questionState) => questionState.error
);

export const selectIsQuestionFormValid = createSelector(
  [selectQuestionFormData],
  (formData) => {
    const hasBasicFields = formData.title.trim().length > 0 && 
                          formData.description.trim().length > 0 &&
                          formData.communityId.length > 0;
    
    if (!hasBasicFields) return false;
    
    if (formData.type === 'NORMAL') {
      console.log(formData.topics.length, 'formLength')
      return formData.topics.length > 0;
    } else {
      return formData.equipmentCategory !== null && 
             formData.equipmentManufacturer !== null && 
             formData.equipmentModel !== null;
    }
  }
);
