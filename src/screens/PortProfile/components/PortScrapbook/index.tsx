import React, { useEffect, useState } from 'react';
import { View, Text, Pressable, FlatList, ActivityIndicator, RefreshControl } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { SectionHeader } from '@/src/components/SectionHeader';
import UserPost from '@/src/components/UserPost';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import {
  fetchScrapbookPosts,
  addScrapbookReactionOptimistic,
  removeScrapbookReactionOptimistic,
  deleteScrapbookPostOptimistic,
  revertDeleteScrapbookPostOptimistic,
} from '@/src/redux/slices/content/contentSlice';
import { RootState, AppDispatch } from '@/src/redux/store';
import { BottomTabNavigationI } from '@/src/navigation/types';
import AnalysisTab from '@/src/assets/svgs/AnalysisTab';
import Education from '@/src/assets/svgs/Education';
import { PostExternalClientI } from '@/src/networks/content/types';
import {
  upsertScrapbookReaction,
  deleteScrapbookReaction,
  deleteScrapbookPost,
} from '@/src/networks/port/scrapbook';
import { ScrapBookPostFetchForClientI } from '@/src/networks/port/types';
import { PortScrapbookProps } from './types';

const PortScrapbook: React.FC<PortScrapbookProps> = ({ unLocode }) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation<BottomTabNavigationI>();
  const currentUser = useSelector(selectCurrentUser);
  const { scrapbookPosts, scrapbookPagination } = useSelector((state: RootState) => state.content);

  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    const loadInitialData = async () => {
      if (!unLocode) return;
      try {
        setInitialLoading(true);
        await dispatch(
          fetchScrapbookPosts({ portUnLocode: unLocode, page: 1, pageSize: 10, refresh: true }),
        ).unwrap();
      } finally {
        setInitialLoading(false);
      }
    };
    loadInitialData();
  }, [dispatch, unLocode]);

  const handleRefresh = async () => {
    if (!unLocode || refreshing) return;
    setRefreshing(true);
    try {
      await dispatch(
        fetchScrapbookPosts({ portUnLocode: unLocode, page: 1, pageSize: 10, refresh: true }),
      ).unwrap();
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (!unLocode || isLoadingMore || !scrapbookPagination.hasMore) return;
    setIsLoadingMore(true);
    try {
      await dispatch(
        fetchScrapbookPosts({
          portUnLocode: unLocode,
          page: scrapbookPagination.page + 1,
          pageSize: 10,
          refresh: false,
        }),
      ).unwrap();
    } finally {
      setIsLoadingMore(false);
    }
  };

  const handleLikePress = async (postId: string, isLiked: boolean) => {
    try {
      if (isLiked) {
        dispatch(removeScrapbookReactionOptimistic({ scrapbookPostId: postId }));
        await deleteScrapbookReaction({ scrapBookPostId: postId });
      } else {
        dispatch(addScrapbookReactionOptimistic({ scrapbookPostId: postId }));
        await upsertScrapbookReaction({ scrapBookPostId: postId, reactionType: 'LIKE' });
      }
    } catch {
      if (isLiked) {
        dispatch(addScrapbookReactionOptimistic({ scrapbookPostId: postId }));
      } else {
        dispatch(removeScrapbookReactionOptimistic({ scrapbookPostId: postId }));
      }
    }
  };

  const handleDeletePost = async (postId: string) => {
    try {
      dispatch(deleteScrapbookPostOptimistic({ scrapbookPostId: postId }));
      await deleteScrapbookPost({ id: postId });
    } catch {
      dispatch(revertDeleteScrapbookPostOptimistic({ postId }));
    }
  };

  const handleEditPress = (postId: string) => {
    navigation.navigate('CreateStack', {
      screen: 'CreateContent',
      params: { editing: true, type: 'SCRAPBOOK_POST', postId, portUnLocode: unLocode },
    });
  };

  const handleCreatePress = () => {
    navigation.navigate('CreateStack', {
      screen: 'CreateContent',
      params: { type: 'SCRAPBOOK_POST', portUnLocode: unLocode, editing: false },
    });
  };

  const transformPost = (item: ScrapBookPostFetchForClientI): PostExternalClientI => {
    const profile = item.profile || {};
    const createdAtString =
      typeof item.createdAt === 'string' ? item.createdAt : new Date().toISOString();
    return {
      id: item.id,
      caption: item.textPreview || '',
      createdAt: createdAtString,
      isCaptionTruncated: item.isCaptionTruncated,
      reactionsCount: item.reactionCount || 0,
      totalCommentsCount: item.commentCount || 0,
      isLiked: item.isLiked || false,
      Profile: {
        id: profile.id || currentUser?.profileId || '',
        name: profile.name || currentUser?.fullName || '',
        avatar: profile.avatar || currentUser?.avatar || null,
        designation: profile.designationText
          ? { name: profile.designationText, id: '', dataType: 'master' }
          : currentUser?.designation || null,
        entity: currentUser?.organisation || null,
      },
      Media: [],
    };
  };

  const renderPost = ({ item }: { item: ScrapBookPostFetchForClientI }) => {
    const formattedPost = transformPost(item);
    const isOwnPost = formattedPost.Profile.id === currentUser?.profileId;
    return (
      <UserPost
        post={formattedPost}
        onLikePress={() => handleLikePress(item.id, item.isLiked || false)}
        onCommentPress={() =>
          navigation.navigate('HomeStack', {
            screen: 'Comment',
            params: { postId: item.id, type: 'SCRAPBOOK_POST', portUnLocode: unLocode },
          })
        }
        onDeletePress={() => handleDeletePost(item.id)}
        onEditPress={() => handleEditPress(item.id)}
        type="SCRAPBOOK_POST"
        onLikeCountPress={() => {
          navigation.navigate('HomeStack', {
            screen: 'Likes',
            params: { postId: item.id, type: 'SCRAPBOOK_POST' },
          });
        }}
        isOwnPost={isOwnPost}
      />
    );
  };

  const renderHeader = () => (
    <View className="flex-row justify-between items-center py-4 px-4 border-b border-gray-100">
      <SectionHeader title="Scrapbook" icon={Education} />
      <Pressable
        onPress={handleCreatePress}
        className="rounded-full border border-green-800 p-2 bg-green-50"
        android_ripple={{ color: '#166534', borderless: true }}
      >
        <AnalysisTab width={2} height={2} fill="#166534" />
      </Pressable>
    </View>
  );

  const renderFooter = () => {
    if (!isLoadingMore) return null;
    return (
      <View className="py-4 justify-center items-center">
        <ActivityIndicator size="small" color="#166534" />
        <Text className="mt-2 text-gray-600 text-sm">Loading more posts...</Text>
      </View>
    );
  };

  const renderEmpty = () => {
    if (initialLoading) return null;
    return (
      <View className="py-8 justify-center items-center">
        <Text className="text-gray-500 mt-4 text-center">No scrapbook posts yet</Text>
        <Text className="text-gray-400 mt-2 text-center text-sm">
          Be the first to share something!
        </Text>
      </View>
    );
  };

  if (initialLoading) {
    return (
      <View className="flex-1 bg-white justify-center items-center">
        <ActivityIndicator size="small" color="#166534" />
        <Text className="mt-4 text-gray-600">Loading scrapbook...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white">
      <FlatList
        data={scrapbookPosts || []}
        renderItem={renderPost}
        keyExtractor={(item) => item?.id || Math.random().toString()}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={renderHeader}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#166534']}
            tintColor="#166534"
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.8}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        contentContainerStyle={scrapbookPosts?.length === 0 ? { flex: 1 } : undefined}
        removeClippedSubviews
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={10}
      />
    </View>
  );
};

export default PortScrapbook;
