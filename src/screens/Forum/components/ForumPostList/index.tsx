/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState } from 'react';
import { View, Text, FlatList, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LottieView from 'lottie-react-native';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import Filter from '@/src/assets/svgs/Filter';
import ForumPost from '../ForumPost';
import type { PreviewIconType, ForumPostProps } from '../ForumPost/types';
import TopBar from '../TopBar';
import NavigationBar from '../../NavigationBar';

const forumPosts: ForumPostProps[] = [
  {
    postId: '1',
    profileId: 'user1',
    type: 'question',
    topics: [
      { id: '1', label: 'Topic 1' },
      { id: '2', label: 'Topic 2' },
    ],
    heading: 'Lorem ipsum dolor sit',
    solved: true,
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam euismod, urna eu tincidunt consectetur, nisi nisl aliquam enim, vitae aliquam nisl nunc euismod nisi.',
    previewIcons: ['photo', 'pdf', 'video'] as PreviewIconType[],
    upVotes: 8,
    downVotes: 2,
    answers: 7,
    endTime: Date.now() + 1000 * 60 * 60 * 23,
    answerView: false,
  },
  {
    postId: '2',
    profileId: 'user2',
    type: 'troubleshooting',
    equipment: [
      { id: '1', label: 'Equipment' },
      { id: '2', label: 'Make' },
      { id: '3', label: 'Model' },
    ],
    heading: 'Lorem ipsum dolor sit amet',
    solved: false,
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque euismod, nisi eu tincidunt consectetur, nisi nisl aliquam enim.',
    previewIcons: ['link'] as PreviewIconType[],
    upVotes: 5,
    downVotes: 1,
    answers: 3,
    endTime: Date.now() + 1000 * 60 * 60 * 1,
    answerView: false,
  },
  {
    postId: '3',
    profileId: 'user3',
    type: 'question',
    topics: [
      { id: '1', label: 'Topic 1' },
      { id: '2', label: 'Topic 2' },
    ],
    heading: 'Lorem ipsum dolor sit amet',
    solved: true,
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, urna eu tincidunt consectetur, nisi nisl aliquam enim.',
    previewIcons: ['excel', 'pdf'] as PreviewIconType[],
    upVotes: 3,
    downVotes: 0,
    answers: 1,
    endTime: Date.now() + 1000 * 60 * 60 * 5,
    answerView: false,
  },
  {
    postId: '4',
    profileId: 'user4',
    type: 'question',
    topics: [
      { id: '1', label: 'Topic 1' },
      { id: '2', label: 'Topic 2' },
    ],
    heading: 'Lorem ipsum dolor sit amet',
    solved: true,
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, urna eu tincidunt consectetur, nisi nisl aliquam enim.',
    previewIcons: ['excel', 'pdf'] as PreviewIconType[],
    upVotes: 3,
    downVotes: 0,
    answers: 1,
    endTime: Date.now() + 1000 * 60 * 60 * 5,
    answerView: false,
  },
];

const ForumPostList = () => {
  const [livePosts, setLivePosts] = useState(true);
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  return (
    <View className="flex-1">
      <TopBar />
      <View className="p-2 flex-1">
        <FlatList
          data={forumPosts}
          keyExtractor={(item) => item.postId}
          renderItem={({ item }) => (
            <Pressable onPress={() => navigation.navigate('ForumAnswers')}>
              <ForumPost post={item} />
            </Pressable>
          )}
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={
            <>
              <NavigationBar />
              <View className="flex-row items-center justify-between py-1 bg-white">
                <Text className="text-lg font-normal text-black px-3">Latest from community</Text>
                <View className="flex-row items-center px-4">
                  <View
                    className="rounded-full justify-center items-center pr-4"
                    style={{ width: 40, height: 40 }}
                  >
                    <Pressable onPress={() => setLivePosts(!livePosts)}>
                      {livePosts && <LottieView
                        source={require('@/src/assets/animations/live.json')}
                        autoPlay
                        loop
                        style={{
                          width: 100,
                          height: 50,
                        }}
                      />}
                      {!livePosts &&
                        <View
                          className='bg-[#448600] rounded-3xl'
                          style={{
                          width: 8,
                          height: 8,
                          }}
                        />
                      }
                    </Pressable>
                  </View>
                  <Pressable>
                    <Filter width={3.0} height={3.0} />
                  </Pressable>
                </View>
              </View>
            </>
          }
        />
      </View>
    </View>
  );
};

export default ForumPostList;
