/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Text, FlatList, Pressable, Image } from 'react-native';
//Testing with a bot avatar
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import UserAvatar from '@/src/components/UserAvatar';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import AiBot from '@/src/assets/images/others/aibot.png';
import Bulb from '@/src/assets/svgs/Bulb';
import Comment from '@/src/assets/svgs/Comment';
import DownVote from '@/src/assets/svgs/DownVote';
import ExcelPreview from '@/src/assets/svgs/ExcelPreview';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import LinkPreview from '@/src/assets/svgs/LinkPreview';
import PathArrow from '@/src/assets/svgs/PathArrow';
import PdfPreview from '@/src/assets/svgs/PdfPreview';
import PhotoPreview from '@/src/assets/svgs/PhotoPreview';
import Share from '@/src/assets/svgs/Share';
import SolvedIcon from '@/src/assets/svgs/SolvedIcon';
import UpVote from '@/src/assets/svgs/UpVote';
import VideoPreview from '@/src/assets/svgs/VideoPreview';
import Timer from '../Timer';
import type { ForumPostProps, PreviewIconType } from './types';

const previewIconMap: Record<PreviewIconType, React.FC> = {
  photo: PhotoPreview,
  pdf: PdfPreview,
  video: VideoPreview,
  excel: ExcelPreview,
  link: LinkPreview,
};

const ForumPost: React.FC<{ post: ForumPostProps }> = ({ post }) => {
  const {
    postId,
    profileId,
    type,
    topics,
    equipment,
    heading,
    solved,
    description,
    previewIcons,
    upVotes,
    downVotes,
    answers,
    comments,
    endTime,
    answerView,
    community = 'CommunityName',
  } = post;
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  return (
    <Pressable onPress={() => navigation.navigate('ForumAnswers')}>
      <View className="bg-white overflow-hidden py-2 border-b border-gray-200 mb-3 rounded-lg">
        <View className="px-2 flex-row gap-2 items-center">
          {type === 'troubleshooting' && equipment && (
            <View className="bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
              <FlatList
                horizontal
                data={equipment}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <View className="flex-row items-center">
                    <Text className="text-[#131313] text-sm font-medium">{item.label}</Text>
                    {item.id !== equipment[equipment.length - 1].id && (
                      <View className="px-2">
                        <PathArrow />
                      </View>
                    )}
                  </View>
                )}
              />
            </View>
          )}
          {type === 'question' && topics && (
            <FlatList
              horizontal
              data={topics}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <View className="bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
                  <Text className="text-[#131313] text-sm font-medium">{item.label}</Text>
                </View>
              )}
            />
          )}
          {solved && (
            <View className="rounded-2xl px-2 py-1 flex-row items-center gap-2">
              <SolvedIcon width={2} height={2} />
            </View>
          )}
        </View>
        <View className="px-3 py-2 flex-row items-center">
          <View style={{ flex: 1 }}>
            <Text
              className="text-[#262626] text-xl font-medium"
              numberOfLines={!answerView ? 1 : undefined}
              ellipsizeMode="tail"
            >
              {heading}
            </Text>
          </View>
        </View>
        <View className="py-1 px-3">
          <Text
            className="text-[#262626] text-base font-normal"
            numberOfLines={!answerView ? 1 : undefined}
            ellipsizeMode="tail"
          >
            {description}
          </Text>
        </View>
        {previewIcons.length > 0 && (
          <View className="px-2">
            <FlatList
              horizontal
              data={previewIcons}
              keyExtractor={(item, idx) => item + idx}
              renderItem={({ item }) => {
                const IconComponent = previewIconMap[item];
                return (
                  <View className="p-2">
                    <IconComponent />
                  </View>
                );
              }}
            />
          </View>
        )}
        <View className="flex-row justify-between items-center px-4">
          <View className="flex-row items-center gap-5">
            <View className="flex-row items-center gap-2">
              <Pressable>
                <UpVote width={2.5} height={2.5} isLiked={true} />
              </Pressable>
              <Text className="text-[#262626] text-sm font-medium">{upVotes}</Text>
            </View>
            <View className="flex-row items-center gap-2">
              <Pressable>
                <DownVote width={2.5} height={2.5} isLiked={false} />
              </Pressable>
              <Text className="text-[#262626] text-sm font-medium">{downVotes}</Text>
            </View>
            {!answerView && (
              <Pressable className="flex-row items-center gap-2">
                <Bulb width={2.5} height={2.5} />
                <Text className="text-[#262626] text-sm font-medium">{answers}</Text>
              </Pressable>
            )}
            {answerView && (
              <Pressable className="flex-row items-center gap-2">
                <Comment width={2.5} height={2.5} color="#525252" />
                <Text className="text-[#262626] text-sm font-medium">{comments}</Text>
              </Pressable>
            )}
          </View>
          <View className="flex-row items-center gap-3">
            <Pressable>
              <Timer endTime={endTime} />
            </Pressable>
            <Pressable>
              <Share width={2.5} height={2.5} />
            </Pressable>
          </View>
        </View>
        {answerView && (
          <View className="flex-row items-center justify-between px-4 py-3">
            <View className="flex-row items-center gap-2">
              <Pressable>
                <UserAvatar
                  avatarUri={Image.resolveAssetSource(AiBot).uri}
                  width={28}
                  height={28}
                />
                {/* Replace with actual user avatar */}
              </Pressable>
              <Text className="text-[#262626] text-base font-normal">
                {profileId} in <Text className="font-medium italic">{community}</Text>
              </Text>
            </View>
            <Pressable>
              <HorizontalEllipsis width={2.5} height={2.5} />
            </Pressable>
          </View>
        )}
      </View>
    </Pressable>
  );
};

export default ForumPost;
