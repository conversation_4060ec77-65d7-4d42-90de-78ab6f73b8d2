/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import Anticlockwise from '@/src/assets/svgs/AntiClockwise';
import type { RecentSearchesProps } from './types';
import { ForumSearchCategory } from '../SearchBox/types';

const dummyRecentSearches = [
  { category: 'posts', searchText: 'engine' },
  { category: 'communities', searchText: 'navigation' },
];

const RecentSearches: React.FC<RecentSearchesProps> = ({
  setSearchData,
  setShowRecent,
  setActiveTab,
  setLoading,
  setLastSearchQuery,
}) => {
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const onPress = (item: { category: string; searchText: string }) => {
    try {
      setSearchData(item.searchText);
      setActiveTab(item.category as ForumSearchCategory);
      setShowRecent(false);
      setLoading(true);
      setLastSearchQuery(item.searchText);
      setTimeout(() => setLoading(false), 300);
    } catch (error) {
      const errorMessage = `Failed to perform recent search: ${error instanceof Error ? error.message : 'Unknown error'}`;
      triggerErrorBoundary(new Error(errorMessage));
    }
  };

  return (
    <View className="flex-1">
      <FlatList
        data={dummyRecentSearches}
        keyExtractor={(item, index) => `${item.searchText}-${index}`}
        renderItem={({ item }) => (
          <Pressable
            onPress={() => onPress(item)}
            className="flex-row items-center py-3 px-4 mt-2"
            android_ripple={{ color: 'transparent' }}
          >
            <View className="items-center justify-center mr-3">
              <Anticlockwise />
            </View>
            <Text className="text-gray-800 text-base font-light">{item.searchText}</Text>
          </Pressable>
        )}
        contentContainerStyle={{ flexGrow: 1 }}
      />
    </View>
  );
};

export default RecentSearches;