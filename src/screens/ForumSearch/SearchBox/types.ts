/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ForumPostProps } from '@/src/screens/Forum/components/ForumPost/types';

export type ForumSearchType = {
    searchType:'general' | 'community';
};

export type ForumSearchCategory = 'posts' | 'communities';

export type ForumSearchResponse = {
  data: ForumPostProps[] | string[];
  total: number;
};

export type ForumSearchBoxPropsI = {
  onBack: () => void;
  onError?: (error: Error) => void;
}