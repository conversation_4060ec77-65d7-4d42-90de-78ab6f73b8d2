/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect, useState } from 'react';
import { Linking } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { pick, types } from '@react-native-documents/picker';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import {
  addIdentityDocument,
  addVisaDocument,
  editIdentityDocument,
  editVisaDocument,
} from '@/src/redux/slices/about/aboutSlice';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { compressToTargetSize } from '@/src/utilities/upload/compress';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { IdTitleI } from '@/src/types/common/data';
import APIResError from '@/src/errors/networks/APIResError';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import {
  addDocumentAPI,
  editIdentityDocumentAPI,
  editVisaDocumentAPI,
  fetchDocumentAPI,
} from '@/src/networks/career/document';
import { AddDocumentBodyI, EditDocumentBodyI } from '@/src/networks/career/types';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import {
  CompressedFileI,
  DocTypeI,
  DocumentFormDataI,
  IdentityDocPayloadI,
  PreSignedUrlIResponse,
  UseEditDocumentItemI,
  VisaDocPayloadI,
} from './types';

export const useEditDocumentItem = (
  _profileId?: string,
  documentId?: string,
  type?: string,
): UseEditDocumentItemI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [accessUrl, setAccessUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState<string | undefined>();
  const [isFileRemoved, setIsFileRemoved] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isPresent, setIsPresent] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) throw error;

  const dispatch = useDispatch();

  const MAX_FILE_SIZE = 5 * 1024 * 1024;

  const documentTypeOptions: IdTitleI[] = [
    { title: 'Identity', id: 'identity' },
    { title: 'Visa', id: 'visa' },
    { title: 'Medical', id: 'medical' },
  ];

  const documentNameOptions: IdTitleI[] = [
    { title: 'Passport', id: 'passport' },
    { title: "CDC (Seamen's Book)", id: 'cdc' },
    { title: 'SID', id: 'sid' },
  ];

  const methods = useForm<DocumentFormDataI>({
    mode: 'onChange',
    defaultValues: {
      documentType: 'Identity',
      documentName: '',
      documentNumber: '',
      country: {},
      validFrom: '',
      validUntil: '',
    },
  });

  useEffect(() => {
    if (documentId) {
      const fetchDocument = async () => {
        try {
          setLoading(true);
          const response = await fetchDocumentAPI(documentId, type!);
          const fetchedDocument: DocumentFormDataI = {
            documentType: response.type ? 'identity' : 'visa',
            documentName: (response.type ? response.type!.toLowerCase() : response.name) as string,
            documentNumber: response.documentNo,
            country: {
              id: response.country.iso2,
              name: response.country.name,
            },
            validFrom: response.fromDate,
            validUntil: response.untilDate,
            documentFile: response.fileUrl,
          };
          if (!response.untilDate) {
            setIsPresent(true);
          }
          methods.reset(fetchedDocument);
          setDownloadUrl(response.fileUrl);
        } catch (err) {
          triggerErrorBoundary(
            new Error(
              `Failed to fetch document: ${err instanceof APIResError ? err.message : 'Unknown error'}`,
            ),
          );
        } finally {
          setLoading(false);
        }
      };

      fetchDocument();
    }
  }, [documentId, methods]);

  const onSubmit = async (data: DocumentFormDataI) => {
    try {
      setIsSubmitting(true);

      let response = {};
      if (documentId) {
        if (data.documentType.toLowerCase() === 'identity') {
          const payload = transformIdentityDocumentData(
            data,
            accessUrl,
            documentId,
            downloadUrl,
            isFileRemoved,
          );
          await editIdentityDocumentAPI(payload as EditDocumentBodyI, documentId);
          dispatch(editIdentityDocument({ ...data, documentId }));
        } else if (data.documentType.toLowerCase() === 'visa') {
          const payload = transformVisaDocumentData(
            data,
            accessUrl,
            documentId,
            downloadUrl,
            isFileRemoved,
          );
          await editVisaDocumentAPI(payload as EditDocumentBodyI, documentId);
          dispatch(editVisaDocument({ ...data, documentId }));
        } else {
          // await addDocumentAPI(payload as AddDocumentBodyI, 'medical');
        }
      } else {
        if (data.documentType.toLowerCase() === 'identity') {
          const payload = transformIdentityDocumentData(data, accessUrl, documentId);
          response = await addDocumentAPI(payload as AddDocumentBodyI, 'identity');
          dispatch(addIdentityDocument({ ...data, ...response }));
        } else if (data.documentType.toLowerCase() === 'visa') {
          const payload = transformVisaDocumentData(data, accessUrl, documentId);
          response = await addDocumentAPI(payload as AddDocumentBodyI, 'visa');
          dispatch(addVisaDocument({ ...data, ...response }));
        } else {
          // response = await addDocumentAPI(payload as AddDocumentBodyI, 'medical');
        }
      }
      showToast({
        message: 'Success',
        description: `Added new Documentation successfully`,
        type: 'success',
      });
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Document',
            description: error instanceof APIResError ? error.message : 'Failed to Save Document',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAttachment = async () => {
    try {
      const [result] = await pick({
        type: [types.pdf, types.images],
      });

      if (!result) {
        showToast({
          message: 'No file selected',
          type: 'error',
        });
        return;
      }

      const allowedMimeTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
      const mimeType = result.type ?? '';

      if (!allowedMimeTypes.includes(mimeType)) {
        showToast({
          message: 'Unsupported file type',
          description: 'Only PDF, JPG, and PNG files are allowed.',
          type: 'error',
        });
        return;
      }

      setSelectedFile(result.name ?? 'Unnamed File');

      let response: PreSignedUrlIResponse = [];
      let compressedFile: CompressedFileI = {
        uri: result.uri,
        type: mimeType,
        filename: result.name ?? `File-${result.size ?? 'unknown'}.jpg`,
      };

      const isPdf = mimeType === 'application/pdf';

      if (!isPdf) {
        const compressedUri = await compressToTargetSize(result.uri, 500);
        compressedFile = {
          uri: compressedUri,
          type: 'image/jpeg',
          filename: result.name ?? `File-${result.size ?? 'unknown'}.jpg`,
        };
      } else {
        if (!result.size || result.size > MAX_FILE_SIZE) {
          showToast({
            message: 'File too large',
            description: `PDF must be under ${MAX_FILE_SIZE / (1024 * 1024)}MB.`,
            type: 'error',
          });
          return;
        }
      }

      const extension = compressedFile.type.split('/')[1];
      response = await fetchPresignedUrlAPI([extension], 'DOCUMENTATION');

      await uploadFileWithPresignedUrl(compressedFile, response[0].uploadUrl);
      setAccessUrl(response[0].accessUrl);
      setIsFileRemoved(false);
    } catch (error) {
      showToast({
        message: 'Error',
        description: 'Media selection failed or upload was cancelled.',
        type: 'error',
      });
    }
  };

  const handleDownload = async () => {
    try {
      if (!downloadUrl) {
        showToast({
          message: 'Error',
          description: 'No File to Download',
          type: 'error',
        });
        return;
      }

      const supported = await Linking.canOpenURL(downloadUrl);

      if (supported) {
        await Linking.openURL(downloadUrl);
        showToast({
          message: 'Success',
          description: 'Download Started',
          type: 'success',
        });
      } else {
        showToast({
          message: 'Error',
          description: 'Download Not Supported',
          type: 'error',
        });
      }
    } catch (error) {
      showToast({
        message: 'Error',
        description: 'Download Failed',
        type: 'error',
      });
    }
  };

  const handleRemoveFile = () => {
    if (selectedFile) {
      setSelectedFile(null);
      setAccessUrl(null);
      showToast({
        message: 'File Removed',
        description: 'The selected file has been removed',
        type: 'success',
      });
    } else if (downloadUrl) {
      setIsFileRemoved(true);
      showToast({
        message: 'File Marked for Removal',
        description: 'The file will be removed when you save the document',
        type: 'success',
      });
    }
  };

  const clearFields = () => {
    dispatch(clearSelection('country'));
  };

  const handlePresentCheckbox = () => {
    setIsPresent(!isPresent);
    methods.setValue('validUntil', null);
  };

  return {
    methods,
    documentTypeOptions,
    documentNameOptions,
    isSubmitting,
    onSubmit,
    navigation,
    handleAttachment,
    selectedFile,
    loading,
    handleDownload,
    handleRemoveFile,
    isFileRemoved,
    clearFields,
    isPresent,
    handlePresentCheckbox,
    isSubmitted,
    setIsSubmitted,
  };
};

const transformIdentityDocumentData = (
  data: DocumentFormDataI,
  fileUrl: string | null,
  documentId?: string,
  existingFileUrl?: string,
  isFileRemoved?: boolean,
) => {
  const payload: IdentityDocPayloadI = {
    documentNo: data.documentNumber,
    type:
      data.documentName === "CDC (Seamen's Book)"
        ? 'CDC'
        : (data.documentName.toUpperCase() as DocTypeI),
    countryIso2: data.country?.id,
    fromDate: data.validFrom,
    untilDate: data.validUntil,
    ...(!Boolean(documentId) && { fileUrl }),
  };

  if (isFileRemoved && existingFileUrl) {
    payload.file = {
      opr: 'DELETE',
    };
  } else if (fileUrl && documentId) {
    payload.file = {
      opr: existingFileUrl ? 'UPDATE' : 'CREATE',
      fileUrl: fileUrl,
    };
  }

  return payload;
};

const transformVisaDocumentData = (
  data: DocumentFormDataI,
  fileUrl: string | null,
  documentId?: string,
  existingFileUrl?: string,
  isFileRemoved?: boolean,
) => {
  const payload: VisaDocPayloadI = {
    documentNo: data.documentNumber,
    name: data.documentName,
    countryIso2: data.country?.id,
    fromDate: data.validFrom,
    untilDate: data.validUntil,
    ...(!Boolean(documentId) && { fileUrl }),
  };

  if (isFileRemoved && existingFileUrl) {
    payload.file = {
      opr: 'DELETE',
    };
  } else if (fileUrl && documentId) {
    payload.file = {
      opr: existingFileUrl ? 'UPDATE' : 'CREATE',
      fileUrl: fileUrl,
    };
  }

  return payload;
};
