import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import CreateQuestionForm from './components/CreateQuestionForm';

const CreateQuestionScreen = () => {
  const navigation = useNavigation<BottomTabNavigationI>();

  const handleSuccess = () => {
    navigation.goBack();
  };

  return (
    <SafeArea>
      <CreateQuestionForm onSuccess={handleSuccess} />
    </SafeArea>
  );
};

export default CreateQuestionScreen;
