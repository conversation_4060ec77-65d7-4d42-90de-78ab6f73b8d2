import { useCallback, useRef, useState } from 'react';
import type { TextInput } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { zodResolver } from '@hookform/resolvers/zod';
import { pick, types } from '@react-native-documents/picker';
import { useForm } from 'react-hook-form';
import { showToast } from '@/src/utilities/toast';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import {
  createQuestionAPI,
  editQuestionAPI,
  fetchQuestionAPI,
} from '@/src/networks/question/question';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import { createQuestionSchema, type QuestionFormData } from './schema';
import type { CreateQuestionFormProps, MediaI } from './types';

const useCreateQuestionForm = ({ onSuccess, editing, questionId }: CreateQuestionFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [attachments, setAttachments] = useState<MediaI[]>([]);

  const titleInputRef = useRef<TextInput>(null);
  const descriptionInputRef = useRef<TextInput>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    reset,
  } = useForm<QuestionFormData>({
    resolver: zodResolver(createQuestionSchema),
    defaultValues: {
      title: '',
      description: '',
    },
    mode: 'onChange',
  });

  const watchedValues = watch();

  const loadQuestionData = useCallback(async () => {
    if (!editing || !questionId) return;

    setIsLoading(true);
    try {
      const questionData = await fetchQuestionAPI(questionId);
      setValue('title', questionData.title);
      setValue('description', questionData.description);

      if (questionData.attachments) {
        const formattedAttachments: MediaI[] = questionData.attachments.map((attachment) => ({
          uri: attachment.fileUrl,
          type: attachment.mimeType,
          filename: attachment.filename,
        }));
        setAttachments(formattedAttachments);
      }
    } catch (error) {
      showToast({
        message: 'Error',
        description: 'Failed to load question data',
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  }, [editing, questionId, setValue]);

  useFocusEffect(
    useCallback(() => {
      loadQuestionData();
    }, [loadQuestionData]),
  );

  const handleAttachments = async () => {
    try {
      const remainingSlots = 5 - attachments.length;
      if (remainingSlots <= 0) {
        showToast({
          message: 'Limit reached',
          description: 'You can only upload up to 5 files.',
          type: 'info',
        });
        return;
      }

      const files = await pick({
        allowMultiSelection: true,
        type: [types.images, types.pdf, types.plainText],
      });

      if (!files || files.length === 0) return;

      const limitedFiles = files.slice(0, remainingSlots);
      const allowedMimeTypes = [
        'image/jpeg',
        'image/png',
        'image/jpg',
        'application/pdf',
        'text/plain',
      ];

      const validFiles = limitedFiles.filter((file) => {
        const mimeType = file.type ?? '';
        return allowedMimeTypes.includes(mimeType);
      });

      if (validFiles.length !== limitedFiles.length) {
        showToast({
          message: 'Some files skipped',
          description: 'Only images, PDFs, and text files are allowed.',
          type: 'info',
        });
      }

      const newAttachments: MediaI[] = validFiles.map((file) => ({
        uri: file.uri,
        type: file.type ?? 'application/octet-stream',
        filename: file.name ?? 'Unknown file',
      }));

      setAttachments((prev) => [...prev, ...newAttachments]);
    } catch (error) {
      if (
        error &&
        typeof error === 'object' &&
        'code' in error &&
        error.code !== 'DOCUMENT_PICKER_CANCELED'
      ) {
        showToast({
          message: 'Error',
          description: 'Failed to select files',
          type: 'error',
        });
      }
    }
  };

  const handleDeleteAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  const uploadAttachments = async (attachments: MediaI[]) => {
    if (attachments.length === 0) return [];

    const extensions = attachments.map((file) => {
      const extension = file.filename.split('.').pop() || 'unknown';
      return extension;
    });

    const response = await fetchPresignedUrlAPI(extensions, 'POST');

    if (!Array.isArray(response) || response.length !== attachments.length) {
      throw new Error('Failed to get upload URLs');
    }

    await Promise.all(
      attachments.map((file, index) => {
        const presignedData = response[index];
        return uploadFileWithPresignedUrl(file, presignedData.uploadUrl);
      }),
    );

    return response.map((item, index) => ({
      fileUrl: item.accessUrl,
      filename: attachments[index].filename,
      mimeType: attachments[index].type,
    }));
  };

  const onSubmit = async (data: QuestionFormData) => {
    setIsSubmitting(true);
    try {
      let uploadedAttachments: any[] = [];

      if (attachments.length > 0) {
        uploadedAttachments = await uploadAttachments(attachments);
      }

      const payload = {
        title: data.title,
        description: data.description,
        ...(uploadedAttachments.length > 0 && { attachments: uploadedAttachments }),
      };

      if (editing && questionId) {
        await editQuestionAPI(payload, questionId);
        showToast({
          message: 'Success',
          description: 'Question updated successfully',
          type: 'success',
        });
      } else {
        await createQuestionAPI(payload);
        showToast({
          message: 'Success',
          description: 'Question created successfully',
          type: 'success',
        });
      }

      onSuccess();
      resetForm();
    } catch (error) {
      showToast({
        message: 'Error',
        description: editing ? 'Failed to update question' : 'Failed to create question',
        type: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    reset();
    setAttachments([]);
  };

  const focusTitleInput = () => {
    titleInputRef.current?.focus();
  };

  const isFormValid =
    isValid && watchedValues.title.trim().length > 0 && watchedValues.description.trim().length > 0;

  return {
    control,
    errors,
    isSubmitting,
    isLoading,
    attachments,
    titleInputRef,
    descriptionInputRef,
    handleSubmit: handleSubmit(onSubmit),
    handleAttachments,
    handleDeleteAttachment,
    focusTitleInput,
    isFormValid,
    watchedValues,
  };
};

export default useCreateQuestionForm;
