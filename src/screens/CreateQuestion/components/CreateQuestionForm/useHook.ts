import { useCallback, useRef, useState } from 'react';
import type { TextInput } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { zodResolver } from '@hookform/resolvers/zod';
import { pick, types } from '@react-native-documents/picker';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { showToast } from '@/src/utilities/toast';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import { createQuestionSchema, type QuestionFormData } from './schema';
import type { CreateQuestionFormProps, MediaI } from './types';
import { AppDispatch } from '@/src/redux/store';
import { createQuestion, updateFormData, setFiles } from '@/src/redux/slices/question/questionSlice';
import {
  selectQuestionFormData,
  selectQuestionLoading,
  selectQuestionError,
  selectIsQuestionFormValid
} from '@/src/redux/selectors/question';
import type { QuestionMediaCreateItemI } from '@/src/redux/slices/question/types';

const useCreateQuestionForm = ({ onSuccess, editing, questionId }: CreateQuestionFormProps) => {
  const dispatch = useDispatch<AppDispatch>();
  const questionFormData = useSelector(selectQuestionFormData);
  const isQuestionLoading = useSelector(selectQuestionLoading);
  const questionError = useSelector(selectQuestionError);
  const isFormValidFromRedux = useSelector(selectIsQuestionFormValid);

  const [attachments, setAttachments] = useState<MediaI[]>([]);

  const titleInputRef = useRef<TextInput>(null);
  const descriptionInputRef = useRef<TextInput>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    reset,
  } = useForm<QuestionFormData>({
    resolver: zodResolver(createQuestionSchema),
    defaultValues: {
      title: questionFormData.title,
      description: questionFormData.description,
    },
    mode: 'onChange',
  });

  const watchedValues = watch();

  // Update form values when Redux data changes
  useFocusEffect(
    useCallback(() => {
      setValue('title', questionFormData.title);
      setValue('description', questionFormData.description);

      if (questionFormData.files.length > 0) {
        const formattedAttachments: MediaI[] = questionFormData.files.map((file) => ({
          uri: file.fileUrl,
          type: file.fileExtension,
          filename: file.fileUrl.split('/').pop() || 'file',
        }));
        setAttachments(formattedAttachments);
      }
    }, [questionFormData, setValue]),
  );

  const handleAttachments = async () => {
    try {
      const remainingSlots = 5 - attachments.length;
      if (remainingSlots <= 0) {
        showToast({
          message: 'Limit reached',
          description: 'You can only upload up to 5 files.',
          type: 'info',
        });
        return;
      }

      const files = await pick({
        allowMultiSelection: true,
        type: [types.images, types.pdf, types.plainText],
      });

      if (!files || files.length === 0) return;

      const limitedFiles = files.slice(0, remainingSlots);
      const allowedMimeTypes = [
        'image/jpeg',
        'image/png',
        'image/jpg',
        'application/pdf',
        'text/plain',
      ];

      const validFiles = limitedFiles.filter((file) => {
        const mimeType = file.type ?? '';
        return allowedMimeTypes.includes(mimeType);
      });

      if (validFiles.length !== limitedFiles.length) {
        showToast({
          message: 'Some files skipped',
          description: 'Only images, PDFs, and text files are allowed.',
          type: 'info',
        });
      }

      const newAttachments: MediaI[] = validFiles.map((file) => ({
        uri: file.uri,
        type: file.type ?? 'application/octet-stream',
        filename: file.name ?? 'Unknown file',
      }));

      setAttachments((prev) => [...prev, ...newAttachments]);
    } catch (error) {
      if (
        error &&
        typeof error === 'object' &&
        'code' in error &&
        error.code !== 'DOCUMENT_PICKER_CANCELED'
      ) {
        showToast({
          message: 'Error',
          description: 'Failed to select files',
          type: 'error',
        });
      }
    }
  };

  const handleDeleteAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  const uploadAttachments = async (attachments: MediaI[]) => {
    if (attachments.length === 0) return [];

    const extensions = attachments.map((file) => {
      const extension = file.filename.split('.').pop() || 'unknown';
      return extension;
    });

    const response = await fetchPresignedUrlAPI(extensions, 'FORUM');

    if (!Array.isArray(response) || response.length !== attachments.length) {
      throw new Error('Failed to get upload URLs');
    }

    await Promise.all(
      attachments.map((file, index) => {
        const presignedData = response[index];
        return uploadFileWithPresignedUrl(file, presignedData.uploadUrl);
      }),
    );

    return response.map((item, index) => ({
      fileUrl: item.accessUrl,
      filename: attachments[index].filename,
      mimeType: attachments[index].type,
    }));
  };

  const onSubmit = async (data: QuestionFormData) => {
    try {
      // Update redux with communityId
      dispatch(updateFormData({
        title: data.title,
        description: data.description,
        communityId: "dda5ebc8-fe88-46ab-a847-139558c3a73b"
      }));

      let uploadedFiles: QuestionMediaCreateItemI[] = [];
      if (attachments.length > 0) {
        const uploadedAttachments = await uploadAttachments(attachments);
        uploadedFiles = uploadedAttachments.map((attachment) => ({
          fileUrl: attachment.fileUrl,
          fileExtension: attachment.filename.split('.').pop() || 'unknown',
        }));
        dispatch(setFiles(uploadedFiles));
      }

      const result = await dispatch(createQuestion());

      if (createQuestion.fulfilled.match(result)) {
        showToast({
          message: 'Success',
          description: 'Question created successfully',
          type: 'success',
        });
        onSuccess();
        resetForm();
      } else {
        throw new Error(result.payload as string);
      }
    } catch (error) {
      showToast({
        message: 'Error',
        description: 'Failed to create question',
        type: 'error',
      });
    }
  };

  const resetForm = () => {
    reset();
    setAttachments([]);
  };

  const focusTitleInput = () => {
    titleInputRef.current?.focus();
  };

  const isFormValid =
   isFormValidFromRedux &&
    isValid &&
    watchedValues.title.trim().length > 0 &&
    watchedValues.description.trim().length > 0;

  return {
    control,
    errors,
    isSubmitting: isQuestionLoading,
    isLoading: isQuestionLoading,
    attachments,
    titleInputRef,
    descriptionInputRef,
    handleSubmit: handleSubmit(onSubmit),
    handleAttachments,
    handleDeleteAttachment,
    focusTitleInput,
    isFormValid,
    watchedValues,
  };
};

export default useCreateQuestionForm;
