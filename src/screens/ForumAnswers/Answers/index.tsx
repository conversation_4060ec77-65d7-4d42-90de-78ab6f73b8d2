/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Text, Pressable, Image } from 'react-native';
import UserAvatar from '@/src/components/UserAvatar';
import AiBot from '@/src/assets/images/others/aibot.png';
import Comment from '@/src/assets/svgs/Comment';
import DownVote from '@/src/assets/svgs/DownVote';
// Testing with a bot avatar
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import Share from '@/src/assets/svgs/Share';
import SolvedIcon from '@/src/assets/svgs/SolvedIcon';
import UpVote from '@/src/assets/svgs/UpVote';
import { ForumAnswerProps } from './types';

const Answers: React.FC<{ answer: ForumAnswerProps }> = ({ answer }) => {
  const {
    answerId,
    // profileId, //use later
    content,
    upVotes,
    downVotes,
    comments,
    answerVerified = false, // Default to false if not provided
  } = answer;
  return (
    <View className="bg-white overflow-hidden py-2 border-b border-gray-200 mb-3 rounded-lg">
      <View className="px-4 flex-row items-center justify-between">
        <View className="flex-row gap-2 items-center">
          <Pressable>
            <UserAvatar avatarUri={Image.resolveAssetSource(AiBot).uri} width={28} height={28} />
            {/* Replace with actual user avatar */}
          </Pressable>
          <Text className="text-base font-normal text-[#262626]">
            {/* Replace with actual user name */}
            User Name
          </Text>
        </View>
        <View className="flex-row gap-2 items-center">
          {answerVerified && <SolvedIcon width={2} height={2} />}
          {!answerVerified && (
            <Pressable>
              <Text className="text-base items-center font-medium text-[#448600]">Verify</Text>
            </Pressable>
          )}
          <Pressable>
            <HorizontalEllipsis width={2.5} height={2.5} />
          </Pressable>
        </View>
      </View>
      <View className="px-4 py-2">
        <Text className="pl-10 text-base font-normal text-[#262626]">{content}</Text>
      </View>
      <View className="flex-row justify-between items-center px-4 py-2">
        <View className="flex-row items-center gap-5 pl-10">
          <View className="flex-row items-center gap-2">
            <Pressable>
              <UpVote width={2.5} height={2.5} isLiked={true} />
            </Pressable>
            <Text className="text-[#262626] text-sm font-medium">{upVotes}</Text>
          </View>
          <View className="flex-row items-center gap-2">
            <Pressable>
              <DownVote width={2.5} height={2.5} isLiked={false} />
            </Pressable>
            <Text className="text-[#262626] text-sm font-medium">{downVotes}</Text>
          </View>
          <View>
            <Pressable className="flex-row items-center gap-2">
              <Comment width={2.5} height={2.5} color="#525252" />
              <Text className="text-[#262626] text-sm font-medium">{comments}</Text>
            </Pressable>
          </View>
        </View>
        <Pressable>
          <Share width={2.5} height={2.5} />
        </Pressable>
      </View>
    </View>
  );
};

export default Answers;
