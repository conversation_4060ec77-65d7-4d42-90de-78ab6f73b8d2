import React from 'react';
import { Text, View, TouchableOpacity, Pressable } from 'react-native';
import { ExploreQnAPropsI, QnATagI } from './types';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';

const ExploreQnA: React.FC<ExploreQnAPropsI> = ({
    title = '',
    tags,
    showAllText = '',
}) => {
    const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>()
    const handleShowAll = () => {
        navigation.navigate("ExploreQna")
    }

    const renderTags = () => {
        return (
            <View className="flex-row flex-wrap gap-2">
                {tags.map((tag) => (
                    <TouchableOpacity
                        key={tag.id}
                        onPress={() => { }}
                        className="bg-white border border-chipGray rounded-full py-3 px-4"
                        activeOpacity={0.7}
                    >
                        <Text className="text-center text-black text-sm">
                            {tag.text}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>
        );
    };

    return (
        <View className="p-4">
            <Text className="text-labelBlack text-lg font-medium leading-5 mb-4">
                {title}
            </Text>

            <View className="mb-4">
                {renderTags()}
            </View>

            <Pressable onPress={handleShowAll}>
                <Text className="text-primaryGreen text-base font-medium">
                    {showAllText}
                </Text>
            </Pressable>
        </View>
    );
};

export default ExploreQnA;