import { useCallback } from 'react';
import { Keyboard } from 'react-native';
import type { RouteProp } from '@react-navigation/native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import type { BottomTabNavigationI, CreateStackParamsListI } from '@/src/navigation/types';
import CreatePostForm from './components/CreatePostForm';

const CreatePostScreen = () => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const { params } = useRoute<RouteProp<CreateStackParamsListI, 'CreateContent'>>();

  useFocusEffect(
    useCallback(() => {
      navigation.getParent()?.setOptions({
        tabBarStyle: { display: 'none' },
      });

      return () => {
        navigation.getParent()?.setOptions({
          tabBarStyle: {
            backgroundColor: '#fff',
            height: 85,
            borderTopWidth: 0,
            paddingHorizontal: 15,
            paddingBottom: 20,
            paddingTop: 20,
            shadowColor: '#333333',
            shadowOffset: {
              width: 0,
              height: -2,
            },
            shadowOpacity: 0.08,
            shadowRadius: 8,
            elevation: 10,
          },
        });
      };
    }, [navigation]),
  );

  const handleSuccess = () => {
    Keyboard.dismiss();
    setTimeout(() => {
      navigation.goBack();
    }, 100);
  };

  return (
    <SafeArea>
      <CreatePostForm
        onSuccess={handleSuccess}
        type={params?.type ?? 'USER_POST'}
        {...(params?.portUnLocode && { portUnLocode: params.portUnLocode })}
        {...(params?.editing && { editing: params.editing })}
        {...(params?.postId && { postId: params.postId })}
      />
    </SafeArea>
  );
};

export default CreatePostScreen;
