import type React from 'react';
import { useState } from 'react';
import { useCallback } from 'react';
import { Keyboard, KeyboardAvoidingView, Platform, TouchableWithoutFeedback } from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import type { AppStackParamListI } from '@/src/navigation/types';
import Searchbox from './components/Searchbox';

const GlobalSearchScreen: React.FC = () => {
  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();
  const [error, setError] = useState<Error | null>(null);

  useFocusEffect(
    useCallback(() => {
      navigation.getParent()?.setOptions({
        tabBarStyle: { display: 'none' },
      });

      return () => {
        navigation.getParent()?.setOptions({
          tabBarStyle: {
            backgroundColor: '#fff',
            height: 85,
            borderTopWidth: 0,
            paddingHorizontal: 15,
            paddingBottom: 20,
            paddingTop: 20,
            shadowColor: '#333333',
            shadowOffset: {
              width: 0,
              height: -2,
            },
            shadowOpacity: 0.08,
            shadowRadius: 8,
            elevation: 10,
          },
        });
      };
    }, [navigation]),
  );

  const handleBack = () => {
    Keyboard.dismiss();
    setTimeout(() => {
      navigation.goBack();
    }, 100);
  };

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ flex: 1, backgroundColor: 'white' }}
        >
          <Searchbox onBack={handleBack} onError={triggerErrorBoundary} />
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default GlobalSearchScreen;
