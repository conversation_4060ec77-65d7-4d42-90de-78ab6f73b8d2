import { useState, useEffect, useRef } from 'react';
import { Platform, PermissionsAndroid, AppState, AppStateStatus } from 'react-native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import { URL } from 'react-native-url-polyfill';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { formatSocialTime } from '@/src/utilities/datetime';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { useSocket } from '@/src/context/providers/SocketProvider';
import {
  findAllSpecificProfileChats,
  deleteSpecificProfileChat,
} from '@/src/networks/chat/individual';
import type { MessageData } from '@/src/networks/chat/types';
import { fetchProfileAPI } from '@/src/networks/profile/userProfile';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import type { MediaPreviewItem, MessageI, ProfileData } from './types';

export const useChatScreenHook = (profileId: string) => {
  const [messages, setMessages] = useState<MessageI[]>([]);
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalMessages, setTotalMessages] = useState(0);
  const [replyPreview, setReplyPreview] = useState<MessageI | null>(null);
  const [selectedMessage, setSelectedMessage] = useState<MessageI | null>(null);
  const [messageText, setMessageText] = useState('');
  const [sending, setSending] = useState(false);
  const [pendingMessages, setPendingMessages] = useState<Map<string, MessageI>>(new Map());
  const [isUserOnline, setIsUserOnline] = useState(false);
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [deleteOptionsVisible, setDeleteOptionsVisible] = useState(false);
  const [clearChatOptions, setClearChatOptionsVisible] = useState(false);
  const [clearChatVisible, setClearChatVisible] = useState(false);
  const [lastSeen, setLastSeen] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [audioDurations, setAudioDurations] = useState<{ [key: string]: number }>({});
  const [linkPreviews, setLinkPreviews] = useState<{ [url: string]: any }>({});
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const [userStatusSent, setUserStatusSent] = useState(false);
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);

  const currentUser = useSelector(selectCurrentUser);
  const flatListRef = useRef<any>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const audioRecorderPlayerRef = useRef<AudioRecorderPlayer | null>(null);
  const statusUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const PAGE_SIZE = 10;
  const STATUS_UPDATE_INTERVAL = 30000;
  const HEARTBEAT_INTERVAL = 15000;

  const { sendMessage: socketSend, subscribeToMessages, isConnected } = useSocket();

  const defaultUserProfile: ProfileData = {
    name: 'User',
    profileId,
    email: '',
    username: '',
    avatar: null,
    designation: null,
    entity: null,
  };

  const currentUserProfile: ProfileData = {
    name: 'You',
    profileId: currentUser.profileId,
    email: '',
    username: '',
    avatar: null,
    designation: null,
    entity: null,
  };

  const sendUserOnlineStatus = (status: 'online' | 'offline' = 'online') => {
    if (!isConnected) return;

    try {
      socketSend('user-status', {
        profileId: currentUser.profileId,
        targetProfileId: profileId,
        status: status,
        timestamp: new Date().toISOString(),
      });

      if (status === 'online') {
        setUserStatusSent(true);
      }
    } catch (error) {
      console.error('Failed to send user status:', error);
    }
  };

  const sendHeartbeat = () => {
    if (!isConnected) return;

    try {
      socketSend('heartbeat', {
        profileId: currentUser.profileId,
        targetProfileId: profileId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to send heartbeat:', error);
    }
  };

  const requestUserStatus = () => {
    if (!isConnected) return;

    try {
      socketSend('request-user-status', {
        profileId: currentUser.profileId,
        targetProfileId: profileId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to request user status:', error);
    }
  };

  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (appState.match(/inactive|background/) && nextAppState === 'active') {
      sendUserOnlineStatus('online');
      requestUserStatus();
    } else if (nextAppState.match(/inactive|background/)) {
      sendUserOnlineStatus('offline');
    }
    setAppState(nextAppState);
  };

  const sendTypingStart = () => {
    if (!isConnected || isTyping) return;

    setIsTyping(true);
    socketSend('typing-start', {
      senderId: currentUser.profileId,
      recieverId: profileId,
      profileId: currentUser.profileId,
    });
  };

  const sendTypingStop = () => {
    if (!isConnected || !isTyping) return;

    setIsTyping(false);
    socketSend('typing-stop', {
      senderId: currentUser.profileId,
      recieverId: profileId,
      profileId: currentUser.profileId,
    });
  };

  const handleTextChange = (text: string) => {
    setMessageText(text);

    if (text.trim() && !isTyping) {
      sendTypingStart();
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        sendTypingStop();
      }
    }, 2000);
  };

  const initializeAudioRecorder = () => {
    if (!audioRecorderPlayerRef.current) {
      audioRecorderPlayerRef.current = new AudioRecorderPlayer();
      audioRecorderPlayerRef.current.setSubscriptionDuration(0.1);
    }
    return audioRecorderPlayerRef.current;
  };

  const requestMediaPermissions = async (): Promise<boolean> => {
    if (Platform.OS === 'android') {
      try {
        const permissions = [PermissionsAndroid.PERMISSIONS.RECORD_AUDIO];

        const grants = await PermissionsAndroid.requestMultiple(permissions);

        const recordAudioGranted =
          grants[PermissionsAndroid.PERMISSIONS.RECORD_AUDIO] ===
          PermissionsAndroid.RESULTS.GRANTED;

        return recordAudioGranted;
      } catch (error) {
        console.error('Permission request failed:', error);
        return false;
      }
    }
    return true;
  };

  const getAudioDuration = async (audioUrl: string): Promise<number> => {
    return new Promise((resolve) => {
      const tempPlayer = new AudioRecorderPlayer();

      tempPlayer
        .startPlayer(audioUrl)
        .then(() => {
          tempPlayer.addPlayBackListener((e) => {
            if (e.duration > 0) {
              tempPlayer.stopPlayer().catch(() => {});
              tempPlayer.removePlayBackListener();
              resolve(e.duration);
            }
          });
        })
        .catch((error) => {
          console.error('Audio duration error:', error);
          resolve(0);
        });

      setTimeout(() => {
        tempPlayer.stopPlayer().catch(() => {});
        tempPlayer.removePlayBackListener();
        resolve(0);
      }, 5000);
    });
  };

  const loadAudioDuration = async (messageId: string, audioUrl: string) => {
    try {
      const duration = await getAudioDuration(audioUrl);
      setAudioDurations((prev) => ({
        ...prev,
        [messageId]: duration,
      }));
    } catch (error) {
      console.error('Load audio duration error:', error);
    }
  };

  const fetchLinkPreview = async (url: string) => {
    if (linkPreviews[url]) {
      return linkPreviews[url];
    }

    try {
      const response = await fetch(url);
      const html = await response.text();

      const getMetaTag = (name: string): string | undefined => {
        const match = html.match(
          new RegExp(
            `<meta(?:.*?)(?:property|name)=["']${name}["'](?:.*?)content=["'](.*?)["']`,
            'i',
          ),
        );
        return match ? match[1] : undefined;
      };

      const title = getMetaTag('og:title') || getMetaTag('twitter:title') || getMetaTag('title');
      const description =
        getMetaTag('og:description') ||
        getMetaTag('twitter:description') ||
        getMetaTag('description');
      const image = getMetaTag('og:image') || getMetaTag('twitter:image');
      const siteName = getMetaTag('og:site_name') || new URL(url).hostname;

      if (title || image) {
        const preview = {
          title,
          description,
          image,
          siteName,
        };

        setLinkPreviews((prev) => ({
          ...prev,
          [url]: preview,
        }));

        return preview;
      }
    } catch (error) {
      console.error('Link preview error:', error);
    }

    return null;
  };

  const normalizeMediaMimeType = (mimeType: string): string => {
    const normalizedType = mimeType.toLowerCase();

    let result = 'JPEG';
    if (normalizedType.startsWith('image/')) {
      result = 'JPEG';
    } else if (normalizedType === 'application/pdf' || normalizedType.includes('pdf')) {
      result = 'PDF';
    } else if (normalizedType === 'text/plain' || normalizedType.includes('txt')) {
      result = 'TEXT';
    } else if (
      normalizedType === 'audio/mpeg' ||
      normalizedType.includes('mp3') ||
      normalizedType === 'audio/mp3'
    ) {
      result = 'MP3';
    } else if (normalizedType === 'audio/m4a' || normalizedType.includes('m4a')) {
      result = 'M4A';
    }

    return result;
  };

  const loadMessages = async (page = 0, isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else if (page > 0) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const response = await findAllSpecificProfileChats({
        profileId,
        page,
        pageSize: PAGE_SIZE,
      });

      if (response.lastSeen) {
        try {
          const lastSeenDate = new Date(response.lastSeen);
          if (!isNaN(lastSeenDate.getTime())) {
            setLastSeen(formatSocialTime(lastSeenDate.getTime()));
          } else {
            setLastSeen(null);
          }
        } catch (error) {
          console.error('Last seen parsing error:', error);
          setLastSeen(null);
        }
      } else {
        setLastSeen(null);
      }

      const messagesWithUser: MessageI[] = response.data.map((msg: MessageData) => ({
        ...msg,
        user:
          msg.senderId === currentUser.profileId
            ? currentUserProfile
            : profile || defaultUserProfile,
      }));

      messagesWithUser.forEach((message) => {
        if (message.content.media) {
          message.content.media.forEach((media: any) => {
            if (
              media.mimeType === 'MP3' ||
              media.mimeType === 'M4A' ||
              media.mimeType.includes('mp3') ||
              media.mimeType.includes('m4a') ||
              media.mimeType === 'audio/mpeg' ||
              media.mimeType === 'audio/m4a'
            ) {
              loadAudioDuration(message.id, media.url);
            }
          });
        }

        if (message.content.text) {
          const urlRegex = /(https?:\/\/[^\s]+)/g;
          const urls = message.content.text.match(urlRegex);
          if (urls) {
            urls.forEach((url) => {
              fetchLinkPreview(url);
            });
          }
        }
      });

      if (isRefresh) {
        setMessages(messagesWithUser);
        setCurrentPage(0);
      } else if (page > 0) {
        setMessages((prev) => [...prev, ...messagesWithUser]);
        setCurrentPage(page);
      } else {
        setMessages(messagesWithUser);
        setCurrentPage(0);
      }

      setTotalMessages(response.total);
      setHasMore(response.hasMore);
    } catch (err) {
      console.error('Load messages error:', err);
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  };

  const refreshMessages = () => {
    loadMessages(0, true);
  };

  const loadProfile = async () => {
    try {
      const profileData = await fetchProfileAPI(profileId);
      setProfile(profileData);
    } catch (err) {
      console.error('Load profile error:', err);
    }
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore && !refreshing) {
      loadMessages(currentPage + 1);
    }
  };

  const uploadMedia = async (selectedMedia: any[]): Promise<MediaPreviewItem[]> => {
    if (!selectedMedia || selectedMedia.length === 0) {
      throw new Error('No media files provided');
    }

    const extensions = selectedMedia
      .map((file) => {
        const mimeType = file.mime || file.type || '';
        if (mimeType.startsWith('image/')) {
          return mimeType.split('/')[1];
        } else if (mimeType === 'audio/mpeg' || mimeType.includes('mp3')) {
          return 'mp3';
        } else if (mimeType === 'audio/m4a' || mimeType.includes('m4a')) {
          return 'm4a';
        } else if (mimeType === 'application/pdf' || mimeType.includes('pdf')) {
          return 'pdf';
        }
        return mimeType.split('/')[1];
      })
      .filter((ext) => ext);

    if (extensions.length === 0) {
      throw new Error('No valid media files');
    }

    try {
      const response = await fetchPresignedUrlAPI(extensions, 'CHAT');

      if (!Array.isArray(response) || response.length !== selectedMedia.length) {
        throw new Error('Failed to get upload URLs');
      }

      await Promise.all(
        selectedMedia.map((file, index) => {
          const presignedData = response[index];
          const fileBlob = {
            uri: file.path || file.uri,
            type: file.mime || file.type,
            filename: file.filename || `media_${index}.${extensions[index]}`,
          };
          return uploadFileWithPresignedUrl(fileBlob, presignedData.uploadUrl);
        }),
      );

      const result = response.map((item, index) => ({
        url: item.accessUrl,
        mimeType: selectedMedia[index].mime || selectedMedia[index].type,
        name: selectedMedia[index].filename || `media_${index}.${extensions[index]}`,
        isUploading: false,
      }));

      return result;
    } catch (error) {
      console.error('Media upload failed:', error);
      throw error;
    }
  };

  const sendMessage = async () => {
    if (!messageText.trim() || sending || !isConnected) {
      return;
    }

    if (isTyping) {
      sendTypingStop();
    }

    const tempId = `temp_${Date.now()}`;
    const currentMessageText = messageText.trim();
    const currentReplyTo = replyPreview?.id || null;

    try {
      setSending(true);

      const tempMessage: MessageI = {
        id: tempId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: { text: currentMessageText, media: [] },
        messageType: 'TEXT',
        replyTo: currentReplyTo,
        createdAt: new Date(),
        deletedForAll: false,
        deletedFor: [],
        user: currentUserProfile,
      };

      setMessages((prev) => [...prev, tempMessage]);
      setPendingMessages((prev) => new Map(prev.set(tempId, tempMessage)));
      setMessageText('');
      setReplyPreview(null);

      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);

      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const urls = currentMessageText.match(urlRegex);
      if (urls) {
        urls.forEach((url) => {
          fetchLinkPreview(url);
        });
      }

      const socketPayload = {
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: currentMessageText,
          media: [],
        },
        replyTo: currentReplyTo,
        profileId: currentUser.profileId,
      };

      socketSend('individual', socketPayload);
    } catch (err) {
      console.error('Send message error:', err);
      setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
      setPendingMessages((prev) => {
        const newMap = new Map(prev);
        newMap.delete(tempId);
        return newMap;
      });
    } finally {
      setSending(false);
    }
  };

  const sendMediaMessage = async (mediaItems: MediaPreviewItem[], captionText?: string) => {
    if (!isConnected || sending || mediaItems.length === 0) {
      return;
    }

    if (isTyping) {
      sendTypingStop();
    }

    const tempId = `temp_${Date.now()}`;
    const currentReplyTo = replyPreview?.id || null;

    try {
      setSending(true);
      const tempMessage: MessageI = {
        id: tempId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: captionText || null,
          media: mediaItems,
        },
        messageType: 'MEDIA',
        replyTo: currentReplyTo,
        createdAt: new Date(),
        deletedForAll: false,
        deletedFor: [],
        user: currentUserProfile,
      };

      setMessages((prev) => [...prev, tempMessage]);
      setPendingMessages((prev) => new Map(prev.set(tempId, tempMessage)));
      if (replyPreview) {
        setReplyPreview(null);
      }
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);

      if (captionText) {
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const urls = captionText.match(urlRegex);
        if (urls) {
          urls.forEach((url) => {
            fetchLinkPreview(url);
          });
        }
      }

      const socketPayload = {
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: captionText || null,
          media: mediaItems.map((item) => ({
            ...item,
            mimeType: normalizeMediaMimeType(item.mimeType),
          })),
        },
        replyTo: currentReplyTo,
        profileId: currentUser.profileId,
      };

      socketSend('individual', socketPayload);
    } catch (err) {
      console.error('Send media message error:', err);
      setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
      setPendingMessages((prev) => {
        const newMap = new Map(prev);
        newMap.delete(tempId);
        return newMap;
      });
    } finally {
      setSending(false);
    }
  };

  const startRecording = async () => {
    try {
      const hasPermission = await requestMediaPermissions();
      if (!hasPermission) {
        console.error('Media recording permission denied');
        return;
      }

      const audioRecorder = initializeAudioRecorder();

      let audioPath: string;
      if (Platform.OS === 'ios') {
        audioPath = 'audio_recording.m4a';
      } else {
        audioPath = `/data/data/com.navicater/cache/${Date.now()}_audio_recording.mp3`;
      }

      await audioRecorder.startRecorder(audioPath);
      setIsRecording(true);

      audioRecorder.addRecordBackListener((e) => {
        const seconds = Math.floor(e.currentPosition / 1000);
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        const timeString = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
      });
    } catch (error) {
      console.error('Start recording error:', error);
      setIsRecording(false);
    }
  };

  const stopRecording = async (shouldSend: boolean = true) => {
    try {
      const audioRecorder = audioRecorderPlayerRef.current;
      if (audioRecorder && isRecording) {
        const result = await audioRecorder.stopRecorder();
        audioRecorder.removeRecordBackListener();

        if (result && shouldSend) {
          const audioFile = {
            path: result,
            uri: result,
            type: Platform.OS === 'ios' ? 'audio/m4a' : 'audio/mp3',
            mime: Platform.OS === 'ios' ? 'audio/m4a' : 'audio/mp3',
            filename: `audio_${Date.now()}.${Platform.OS === 'ios' ? 'm4a' : 'mp3'}`,
          };

          try {
            const mediaItems = await uploadMedia([audioFile]);
            await sendMediaMessage(mediaItems, messageText.trim());
          } catch (err) {
            console.error('Send audio message error:', err);
          }
        }
      }
    } catch (error) {
      console.error('Stop recording error:', error);
    } finally {
      setIsRecording(false);
    }
  };

  const editMessage = async (messageId: string, newContent: string) => {
    try {
      const message = messages.find((msg) => msg.id === messageId);
      if (!message) {
        return;
      }

      const updatedContent = {
        text: newContent,
        media: message.content.media || [],
      };

      socketSend('edit-message', {
        id: messageId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: updatedContent,
        profileId: currentUser.profileId,
      });

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId ? { ...msg, content: updatedContent, editedAt: new Date() } : msg,
        ),
      );

      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const urls = newContent.match(urlRegex);
      if (urls) {
        urls.forEach((url) => {
          fetchLinkPreview(url);
        });
      }
    } catch (err) {
      console.error('Edit message error:', err);
    }
  };

  const deleteManyForMe = async () => {
    const messageIds = Array.from(selectedMessages);

    if (messageIds.length === 0) {
      return;
    }

    try {
      socketSend('delete-for-me', {
        ids: messageIds,
        senderId: currentUser.profileId,
        recieverId: profileId,
        profileId: currentUser.profileId,
        type: 'FOR_ME',
      });

      setMessages((prev) => prev.filter((msg) => !messageIds.includes(msg.id)));
      setSelectedMessages(new Set());
      setIsSelectionMode(false);
    } catch (err) {
      console.error('Delete messages for me error:', err);
    }
  };

  const deleteManyForEveryone = async () => {
    const messageIds = Array.from(selectedMessages);

    if (messageIds.length === 0) {
      return;
    }

    try {
      socketSend('delete-for-everyone', {
        ids: messageIds,
        senderId: currentUser.profileId,
        recieverId: profileId,
        profileId: currentUser.profileId,
        type: 'FOR_EVERYONE',
      });

      setMessages((prev) =>
        prev.map((msg) =>
          messageIds.includes(msg.id)
            ? {
                ...msg,
                deletedForAll: true,
                content: { ...msg.content, text: 'This message has been deleted' },
              }
            : msg,
        ),
      );
      setSelectedMessages(new Set());
      setIsSelectionMode(false);
    } catch (err) {
      console.error('Delete messages for everyone error:', err);
    }
  };

  const clearChat = async () => {
    try {
      await deleteSpecificProfileChat(profileId);
      setMessages([]);
      setClearChatVisible(false);
    } catch (err) {
      console.error('Clear chat error:', err);
    }
  };

  const handleMessageLongPress = (message: MessageI) => {
    if (isSelectionMode) {
      toggleMessageSelection(message.id);
    } else {
      setSelectedMessage(message);
    }
  };

  const handleMessagePress = (message: MessageI) => {
    if (isSelectionMode) {
      toggleMessageSelection(message.id);
    }
  };

  const toggleMessageSelection = (messageId: string) => {
    setSelectedMessages((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }

      if (newSet.size === 0) {
        setIsSelectionMode(false);
      }

      return newSet;
    });
  };

  const startSelectionMode = (messageId: string) => {
    setIsSelectionMode(true);
    setSelectedMessages(new Set([messageId]));
  };

  const exitSelectionMode = () => {
    setIsSelectionMode(false);
    setSelectedMessages(new Set());
  };

  const handleDeleteSelected = () => {
    if (selectedMessages.size > 0) {
      setDeleteOptionsVisible(true);
    }
  };

  const handleDeleteSelectedForMe = () => {
    const messageIds = Array.from(selectedMessages);
    if (messageIds.length === 0) return;
    deleteManyForMe();
    setDeleteOptionsVisible(false);
    setIsSelectionMode(false);
  };

  const handleDeleteSelectedForEveryone = () => {
    const messageIds = Array.from(selectedMessages);
    if (messageIds.length === 0) return;
    const canDeleteForEveryone = messageIds.every((id) => {
      const message = messages.find((msg) => msg.id === id);
      return (
        message && message.senderId === currentUser.profileId && canDeleteForEveryoneCheck(message)
      );
    });

    if (canDeleteForEveryone) {
      deleteManyForEveryone();
    }
    setDeleteOptionsVisible(false);
    setIsSelectionMode(false);
  };

  const handleConfirmClearChat = () => {
    clearChat();
  };

  const canDeleteForEveryoneCheck = (message: MessageI): boolean => {
    if (message.senderId !== currentUser.profileId || message.deletedForAll) return false;
    const deleteTimeLimit = new Date(message.createdAt);
    deleteTimeLimit.setMinutes(deleteTimeLimit.getMinutes() + 30);
    return new Date() <= deleteTimeLimit;
  };

  const canDeleteSelectedForEveryone = (): boolean => {
    return Array.from(selectedMessages).every((id) => {
      const message = messages.find((msg) => msg.id === id);
      return (
        message && message.senderId === currentUser.profileId && canDeleteForEveryoneCheck(message)
      );
    });
  };

  const handleReply = () => {
    if (selectedMessage) {
      setReplyPreview(selectedMessage);
    }
    setSelectedMessage(null);
  };

  const handleSwipeReply = (message: MessageI) => {
    setReplyPreview(message);
  };

  const handleCloseReply = () => {
    setReplyPreview(null);
  };

  const formatAudioTime = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleSocketMessage = (type: string, data: any, rawMessage: any) => {
    switch (type) {
      case 'individual-chat':
      case 'individual':
        const messageWithUser: MessageI = {
          ...data,
          user:
            data.senderId === currentUser.profileId
              ? currentUserProfile
              : profile || defaultUserProfile,
        };

        if (messageWithUser.content.media) {
          messageWithUser.content.media.forEach((media: any) => {
            if (
              media.mimeType === 'MP3' ||
              media.mimeType === 'M4A' ||
              media.mimeType.includes('mp3') ||
              media.mimeType.includes('m4a') ||
              media.mimeType === 'audio/mpeg' ||
              media.mimeType === 'audio/m4a'
            ) {
              loadAudioDuration(messageWithUser.id, media.url);
            }
          });
        }

        if (messageWithUser.content.text) {
          const urlRegex = /(https?:\/\/[^\s]+)/g;
          const urls = messageWithUser.content.text.match(urlRegex);
          if (urls) {
            urls.forEach((url) => {
              fetchLinkPreview(url);
            });
          }
        }

        const tempMessage = Array.from(pendingMessages.values()).find(
          (msg) =>
            msg.senderId === data.senderId &&
            msg.recieverId === data.recieverId &&
            (msg.content.text === data.content.text || (msg.content.media && data.content.media)),
        );

        if (tempMessage) {
          setMessages((prev) =>
            prev.map((msg) => (msg.id === tempMessage.id ? messageWithUser : msg)),
          );
          setPendingMessages((prev) => {
            const newMap = new Map(prev);
            newMap.delete(tempMessage.id);
            return newMap;
          });
        } else {
          setMessages((prev) => [...prev, messageWithUser]);
        }

        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
        break;

      case 'typing-start':
        if (
          data.senderId !== currentUser.profileId &&
          (data.senderId === profileId || data.recieverId === currentUser.profileId)
        ) {
          setOtherUserTyping(true);
        }
        break;

      case 'typing-stop':
        if (
          data.senderId !== currentUser.profileId &&
          (data.senderId === profileId || data.recieverId === currentUser.profileId)
        ) {
          setOtherUserTyping(false);
        }
        break;

      case 'message-error':
        const failedMessage = Array.from(pendingMessages.values()).find(
          (msg) =>
            msg.senderId === data.originalSenderId && msg.recieverId === data.originalRecieverId,
        );

        if (failedMessage) {
          setMessages((prev) => prev.filter((msg) => msg.id !== failedMessage.id));
          setPendingMessages((prev) => {
            const newMap = new Map(prev);
            newMap.delete(failedMessage.id);
            return newMap;
          });
        }
        break;

      case 'message-deleted':
        if (data.type === 'FOR_ME') {
          if (data.senderId === currentUser.profileId) {
            if (Array.isArray(data.ids)) {
              setMessages((prev) => prev.filter((msg) => !data.ids.includes(msg.id)));
            } else if (data.id) {
              setMessages((prev) => prev.filter((msg) => msg.id !== data.id));
            }
          }
        } else if (data.type === 'FOR_EVERYONE') {
          if (Array.isArray(data.ids)) {
            setMessages((prev) =>
              prev.map((msg) =>
                data.ids.includes(msg.id)
                  ? {
                      ...msg,
                      deletedForAll: true,
                      content: { ...msg.content, text: 'This message has been deleted' },
                    }
                  : msg,
              ),
            );
          } else if (data.id) {
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === data.id
                  ? {
                      ...msg,
                      deletedForAll: true,
                      content: { ...msg.content, text: 'This message has been deleted' },
                    }
                  : msg,
              ),
            );
          }
        }
        break;

      case 'message-edited':
        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === data.id ? { ...msg, content: data.content, editedAt: new Date() } : msg,
          ),
        );

        if (data.content.text) {
          const urlRegex = /(https?:\/\/[^\s]+)/g;
          const urls = data.content.text.match(urlRegex);
          if (urls) {
            urls.forEach((url: string) => {
              fetchLinkPreview(url);
            });
          }
        }
        break;

      case 'user-status':
        if (data.profileId === profileId || data.targetProfileId === currentUser.profileId) {
          setIsUserOnline(data.status === 'online');
          if (data.status === 'offline' && data.lastSeen) {
            try {
              const lastSeenDate = new Date(data.lastSeen);
              if (!isNaN(lastSeenDate.getTime())) {
                setLastSeen(formatSocialTime(lastSeenDate.getTime()));
              } else {
                setLastSeen(null);
              }
            } catch (error) {
              console.error('User status parsing error:', error);
              setLastSeen(null);
            }
          } else if (data.status === 'online') {
            setLastSeen(null);
          }
        }
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    const initializeChat = async () => {
      try {
        await Promise.all([loadProfile(), loadMessages(0)]);
      } catch (error) {
        console.error('Initialize chat error:', error);
        setLoading(false);
      }
    };

    initializeChat();
  }, [profileId]);

  useEffect(() => {
    if (isConnected && !userStatusSent) {
      sendUserOnlineStatus('online');
      requestUserStatus();
    }
  }, [isConnected, userStatusSent]);

  useEffect(() => {
    if (!isConnected) {
      setUserStatusSent(false);
      if (statusUpdateIntervalRef.current) {
        clearInterval(statusUpdateIntervalRef.current);
        statusUpdateIntervalRef.current = null;
      }
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }
      return;
    }

    statusUpdateIntervalRef.current = setInterval(() => {
      sendUserOnlineStatus('online');
    }, STATUS_UPDATE_INTERVAL);

    heartbeatIntervalRef.current = setInterval(() => {
      sendHeartbeat();
    }, HEARTBEAT_INTERVAL);

    const messageTypes = [
      'individual-chat',
      'individual',
      'typing-start',
      'typing-stop',
      'message-error',
      'message-deleted',
      'message-edited',
      'user-status',
      'heartbeat-response',
      'user-status-response',
    ];

    const unsubscribe = subscribeToMessages(messageTypes, handleSocketMessage);

    return () => {
      unsubscribe();
      if (statusUpdateIntervalRef.current) {
        clearInterval(statusUpdateIntervalRef.current);
      }
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
    };
  }, [isConnected, subscribeToMessages]);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [appState]);

  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (statusUpdateIntervalRef.current) {
        clearInterval(statusUpdateIntervalRef.current);
      }
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
      if (isTyping) {
        sendTypingStop();
      }
      if (isConnected) {
        sendUserOnlineStatus('offline');
      }
      if (audioRecorderPlayerRef.current) {
        audioRecorderPlayerRef.current.stopRecorder().catch(() => {});
        audioRecorderPlayerRef.current.stopPlayer().catch(() => {});
        audioRecorderPlayerRef.current.removeRecordBackListener();
        audioRecorderPlayerRef.current.removePlayBackListener();
      }
    };
  }, []);

  return {
    messages,
    profile,
    loading,
    loadingMore,
    refreshing,
    replyPreview,
    selectedMessage,
    messageText,
    sending,
    flatListRef,
    isUserOnline,
    lastSeen,
    selectedMessages,
    isSelectionMode,
    deleteOptionsVisible,
    clearChatVisible,
    isRecording,
    audioDurations,
    otherUserTyping,
    setMessageText: handleTextChange,
    setSelectedMessage,
    setDeleteOptionsVisible,
    setClearChatVisible,
    sendMessage,
    sendMediaMessage,
    uploadMedia,
    editMessage,
    handleDeleteSelectedForEveryone,
    handleDeleteSelectedForMe,
    handleLoadMore,
    refreshMessages,
    handleMessageLongPress,
    handleMessagePress,
    handleReply,
    handleSwipeReply,
    handleCloseReply,
    startSelectionMode,
    exitSelectionMode,
    handleDeleteSelected,
    handleConfirmClearChat,
    canDeleteSelectedForEveryone,
    clearChatOptions,
    setClearChatOptionsVisible,
    startRecording,
    stopRecording,
    formatAudioTime,
    loadAudioDuration,
    setMessages,
    setReplyPreview,
    requestMediaPermissions,
  };
};
