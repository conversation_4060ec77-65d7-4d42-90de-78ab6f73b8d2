import { useCallback, useState } from 'react';
import React from 'react';
import { ActivityIndicator, FlatList, Pressable, Text, View } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import TextView from '@/src/components/TextView';
import { ExperienceFetchForClientResultI } from '@/src/redux/slices/experience/types';
import { getYearsMonths } from '@/src/utilities/datetime';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { EditExperienceListPropsI } from './types';
import useEditExperienceList from './useHook';

const EditExperienceList = ({ profileId, onBack }: EditExperienceListPropsI) => {
  const {
    isLoading,
    isModalVisible,
    handleModal,
    experiences,
    onAddExperience,
    onEditExperience,
    onDeleteExperience,
  } = useEditExperienceList(profileId);
  const [experienceId, setExperienceId] = useState<null | string>(null);

  const handleDelete = (id: string) => {
    handleModal();
    setExperienceId(id);
  };

  const handleConfirm = () => {
    onDeleteExperience(experienceId as string);
  };

  const renderExperienceItem = ({
    item,
    index,
  }: {
    item: ExperienceFetchForClientResultI;
    index: number;
  }) => {
    const duration = getYearsMonths(item.years, item.months);
    return (
      <View
        className={`${index === experiences.length - 1 ? `` : `border-b border-[#D4D4D4]`} mx-2 p-4`}
      >
        <View className="flex-row flex-wrap justify-between items-center">
          <View className="w-3/4">
            <TextView
              title={item.entity.name}
              subtitle={duration}
              subtitleClassName="font-medium leading-22 text-[#000000] text-sm"
              titleClassName="text-lg"
              ellipsizeMode="tail"
              numberOfLines={1}
            />
          </View>
          <View className="flex-row">
            <Pressable onPress={() => onEditExperience(item.id)} className="p-2">
              <EditPencil width={2.3} height={2.3} />
            </Pressable>
            <Pressable onPress={() => handleDelete(item.id)} className="p-2">
              <DeleteIcon width={2.3} height={2.3} />
            </Pressable>
          </View>
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <>
      <View className="flex-1">
        <View className="flex-row items-center justify-between px-4">
          <View className="flex-row items-center gap-1">
            <BackButton onBack={onBack} label="" />
            <Text className="text-xl font-medium">Edit Experiences</Text>
          </View>
          <View>
            <Pressable onPress={onAddExperience}>
              <AddItem width={4} height={4} />
            </Pressable>
          </View>
        </View>

        {experiences.length > 0 ? (
          <FlatList
            data={experiences}
            renderItem={({ item, index }) => renderExperienceItem({ item, index })}
            keyExtractor={(item) => item.id}
            className="mt-5"
          />
        ) : (
          <NotFound imageStyle={{ height: 120, width: 120 }} />
        )}
      </View>
      <View>
        <CustomModal
          isVisible={isModalVisible}
          title="Confirm Action"
          description={'Are you sure you want to delete?'}
          cancelText="Cancel"
          confirmText="Confirm"
          confirmButtonVariant="danger"
          onConfirm={handleConfirm}
          onCancel={handleModal}
          isConfirming={isLoading}
        />
      </View>
    </>
  );
};

export default EditExperienceList;
