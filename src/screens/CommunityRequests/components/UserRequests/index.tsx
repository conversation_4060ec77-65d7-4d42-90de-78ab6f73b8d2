import BackButton from '@/src/components/BackButton'
import { useState } from 'react'
import { ActivityIndicator, Pressable, Text, View } from 'react-native'
import RequestsHeader from '../RequestsHeader'
import UsersList from '@/src/components/UsersList'
import { useNavigation, useRoute } from '@react-navigation/native'
import { ListItem } from '@/src/components/UsersList/types'
import Tick from '@/src/assets/svgs/Tick'
import Close from '@/src/assets/svgs/Close'
import NotFound from '@/src/components/NotFound'
import CustomModal from '@/src/components/Modal'
import { ItemsI, UserRequestsNavigationI, UserRequestsRouteI } from './types'
import { useUserRequests } from './useHook'

const ROLES = ['member', 'contributor', 'moderator', 'admin'] as const;
type Role = typeof ROLES[number];

const UserRequests = () => {
  const navigation = useNavigation<UserRequestsNavigationI>();
  const route = useRoute<UserRequestsRouteI>();
  const { forumId } = route.params || {};

  const { data, loading, loadMore, refresh, refreshing, handleRequest, requestStatus } =
    useUserRequests({
      forumId: forumId || 'default-forum',
    });

    const [modalVisible, setModalVisible] = useState(false);
    const [pendingAction, setPendingAction] = useState<{
        item: ItemsI | null;
        status: 'ACCEPTED' | 'REJECTED' | null;
    }>({ item: null, status: null });
    const [isConfirming, setIsConfirming] = useState(false);
    const [selectedRole, setSelectedRole] = useState<Role>('member');

  const handleBack = () => {
    navigation.goBack();
  };

  const handleUserPress = (user: ListItem) => {
    console.log('User pressed:', user);
  };

    const openConfirmModal = (item: ItemsI, status: 'ACCEPTED' | 'REJECTED') => {
        setPendingAction({ item, status });
        if (item.requestedRole) {
            setSelectedRole(item.requestedRole || 'member');
        }
        setModalVisible(true);
    };

    const closeConfirmModal = () => {
        setModalVisible(false);
        setPendingAction({ item: null, status: null });
        setSelectedRole('member');
    };

  const confirmAction = async () => {
    if (!pendingAction.item || !pendingAction.status) return;
    const profileId = pendingAction.item.Profile?.id;
    if (!profileId) return;

        setIsConfirming(true);
        try {
            await handleRequest(
                pendingAction.item,
                pendingAction.status,
            );
        } finally {
            setIsConfirming(false);
            closeConfirmModal();
        }
    };

    const renderRoleSelector = () => {
        if (pendingAction.status !== 'ACCEPTED') return null;

        return (
            <View className="mb-4">
                <View className='mb-2 flex-row items-center'>
                    <Text>Expected Role: </Text>
                    <View className="px-3 py-1 bg-transparent border border-primaryGreen rounded-lg mt-1">
                        <Text className="text-gray-700 font-medium capitalize">
                            Admin
                        </Text>
                    </View>
                </View>
                <Text className="text-base font-medium text-gray-900 mb-3">Assign Role:</Text>
                <View className="flex-row flex-wrap gap-2">
                    {ROLES.map((role) => (
                        <Pressable
                            key={role}
                            onPress={() => setSelectedRole(role)}
                            className={`px-3 py-2 rounded-lg border ${selectedRole === role
                                ? 'bg-primaryGreen border-primaryGreen'
                                : 'bg-gray-100 border-gray-300'
                                }`}
                        >
                            <Text
                                className={`text-sm font-medium capitalize ${selectedRole === role ? 'text-white' : 'text-gray-700'
                                    }`}
                            >
                                {role}
                            </Text>
                        </Pressable>
                    ))}
                </View>
            </View>
        );
    };

    const renderRequestedRole = () => {
        if (!pendingAction.item?.requestedRole) return null;

        return (
            <View className="mb-4">
                <Text className="text-base font-medium text-gray-900 mb-2">Requested Role:</Text>
                <View className="px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg">
                    <Text className="text-blue-800 font-medium capitalize">
                        {pendingAction.item.requestedRole}
                    </Text>
                </View>
            </View>
        );
    };

    const renderModalBody = () => {
        if (pendingAction.status === 'REJECTED') return null;

        return (
            <View>
                {renderRequestedRole()}
                {renderRoleSelector()}
            </View>
        );
    };

  const renderActions = (item: ListItem) => {
    const profileId = item.Profile?.id;
    if (!profileId) return null;
    const status = requestStatus[profileId];

    if (status === 'ACCEPTED') {
      return (
        <View className="my-3">
          <Text className="text-primaryGreen font-semibold">Request Accepted</Text>
        </View>
      );
    }

    if (status === 'REJECTED') {
      return (
        <View className="my-3">
          <Text className="text-red-600 font-semibold">Request Rejected</Text>
        </View>
      );
    }

    return (
      <View className="flex-row my-3">
        <Pressable
          onPress={(e) => {
            e.stopPropagation();
            openConfirmModal(item, 'ACCEPTED');
          }}
          className="py-2 px-3 bg-primaryGreen rounded-lg mr-2"
        >
          <View className="flex-row items-center gap-1">
            <Tick width={1.75} height={1.75} color="white" />
            <Text className="text-white">Accept</Text>
          </View>
        </Pressable>

        <Pressable
          onPress={(e) => {
            e.stopPropagation();
            openConfirmModal(item, 'REJECTED');
          }}
          className="py-2 px-3 bg-red-700 rounded-lg"
        >
          <View className="flex-row items-center gap-1">
            <Close width={1.75} height={1.75} color="white" />
            <Text className="text-white">Reject</Text>
          </View>
        </Pressable>
      </View>
    );
  };

  const getModalContent = () => {
    if (!pendingAction.item || !pendingAction.status)
      return { title: '', description: '', confirmText: '' };

    const userName = pendingAction.item.Profile?.name || 'this user';

        if (pendingAction.status === 'ACCEPTED') {
            return {
                title: 'Accept Join Request',
                description: `Accept the join request from ${userName} and assign them a role.`,
                confirmText: 'Accept',
            };
        } else {
            return {
                title: 'Reject Join Request',
                description: `Are you sure you want to reject the join request from ${userName}?`,
                confirmText: 'Reject',
            };
        }
    };

  const modalContent = getModalContent();

    if (loading && !data.length) {
        return (
            <View className="flex-1 bg-white">
                <View className="flex-row items-center px-4 py-3 border-b border-gray-100">
                    <BackButton onBack={handleBack} label="" />
                    <Text className="text-xl font-semibold text-gray-900 ml-2">User Requests</Text>
                </View>
                <View className="flex-1 justify-center items-center">
                    <ActivityIndicator size="small" />
                </View>
            </View>
        );
    }

    if (!data.length) {
        return (
            <View className="flex-1 bg-white">
                <View className="flex-row items-center px-4 py-3 border-b border-gray-100">
                    <BackButton onBack={handleBack} label="" />
                    <Text className="text-xl font-semibold text-gray-900 ml-2">User Requests</Text>
                </View>
                <NotFound
                    title="No pending requests"
                    subtitle="When users request to join, they will appear here"
                />
            </View>
        );
    }
    return (
        <View className="flex-1 bg-white">
            <RequestsHeader handleBack={handleBack} />
            <UsersList
                data={data}
                loading={loading}
                onLoadMore={loadMore}
                onPress={handleUserPress}
                onRefresh={refresh}
                refreshing={refreshing}
                renderActions={renderActions}
            />

            <CustomModal
                isVisible={modalVisible}
                title={modalContent.title}
                description={modalContent.description}
                confirmText={modalContent.confirmText}
                confirmButtonVariant={modalContent.confirmText === 'Accept' ? 'default' : 'danger'}
                cancelText="Cancel"
                onConfirm={confirmAction}
                onCancel={closeConfirmModal}
                isConfirming={isConfirming}
                bodyComponent={renderModalBody()}
            />
        </View>
    )
}

export default UserRequests;
