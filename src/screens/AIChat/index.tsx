import { useCallback } from 'react';
import { KeyboardAvoidingView, Platform, Keyboard } from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import RateLimitModal from '@/src/components/RateLimitModal';
import SafeArea from '@/src/components/SafeArea';
import { ChatHeader } from './components/ChatHeader';
import { useHeader } from './components/ChatHeader/useHeader';
import { ChatOptions } from './components/ChatOptions';
import { MessageInput } from './components/MessageInput';
import { MessageList } from './components/MessageList';
import { useMessageList } from './components/MessageList/useMessageList';
import { useAIChat } from './useAIChat';

const AIChatScreen = () => {
  const navigation = useNavigation();
  const {
    messages,
    messageText,
    setMessageText,
    loading,
    isBottomSheetOpen,
    setIsBottomSheetOpen,
    scrollRef,
    handleSend,
    handleClearChat,
    showRateLimitModal,
    setShowRateLimitModal,
    streamedText,
    currentAiMessageId,
  } = useAIChat();

  const { messageGroups } = useMessageList(messages);
  const { handleBackPress } = useHeader();

  useFocusEffect(
    useCallback(() => {
      navigation.getParent()?.setOptions({
        tabBarStyle: { display: 'none' },
      });

      return () => {
        navigation.getParent()?.setOptions({
          tabBarStyle: {
            backgroundColor: '#fff',
            height: 85,
            borderTopWidth: 0,
            paddingHorizontal: 15,
            paddingBottom: 20,
            paddingTop: 20,
            shadowColor: '#333333',
            shadowOffset: {
              width: 0,
              height: -2,
            },
            shadowOpacity: 0.08,
            shadowRadius: 8,
            elevation: 10,
          },
        });
      };
    }, [navigation]),
  );

  const handleBack = () => {
    Keyboard.dismiss();
    setTimeout(() => {
      handleBackPress();
    }, 100);
  };

  return (
    <SafeArea>
      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <ChatHeader onMenuPress={() => setIsBottomSheetOpen(true)} onBackPress={handleBack} />
        <MessageList
          messages={messages}
          loading={loading}
          scrollRef={scrollRef}
          messageGroups={messageGroups}
          streamedText={streamedText}
          currentAiMessageId={currentAiMessageId}
        />
        <MessageInput
          value={messageText}
          onChangeText={setMessageText}
          onSend={handleSend}
          loading={loading}
        />
        <ChatOptions
          visible={isBottomSheetOpen}
          onClose={() => setIsBottomSheetOpen(false)}
          onClearChat={handleClearChat}
        />
        <RateLimitModal
          onClose={() => setShowRateLimitModal(!showRateLimitModal)}
          visible={showRateLimitModal}
        />
      </KeyboardAvoidingView>
    </SafeArea>
  );
};

export default AIChatScreen;
