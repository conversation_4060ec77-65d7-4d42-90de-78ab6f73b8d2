
import { Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import RadioButton from '@/src/components/RadioButton';
import ToggleSwitch from '@/src/components/Toggle';
import { setQuestionType, setIsAnonymous } from '@/src/redux/slices/question/questionSlice';
import { selectQuestionType, selectQuestionIsAnonymous } from '@/src/redux/selectors/question';
import { AppDispatch } from '@/src/redux/store';
import QnaFields from '../QnaFields';
import TroubleshootingFields from '../TroubleshootingFields';

const AskQuestionForm = () => {
  const dispatch = useDispatch<AppDispatch>();
  const questionType = useSelector(selectQuestionType);
  const isAnonymous = useSelector(selectQuestionIsAnonymous);

  const isQnA = questionType === 'NORMAL';

  const handleTypeChange = (type: 'qna' | 'troubleshoot') => {
    dispatch(setQuestionType(type === 'qna' ? 'NORMAL' : 'TROUBLESHOOT'));
  };

  const handleAnonymousToggle = () => {
    dispatch(setIsAnonymous(!isAnonymous));
  };

  return (
    <View className="mt-6 flex-1 gap-3">
      <Text className="text-xl font-medium">Ask Question</Text>
      <View>
        <Text className="text-sm leading-4 my-3">Post type</Text>
        <View className="flex-row justify-between">
          <View className="flex-1">
            <RadioButton
              selected={questionType === 'NORMAL'}
              onPress={() => handleTypeChange('qna')}
              label="QnA"
            />
          </View>
          <View className="flex-1">
            <RadioButton
              selected={questionType === 'TROUBLESHOOT'}
              onPress={() => handleTypeChange('troubleshoot')}
              label="Troubleshooting"
            />
          </View>
        </View>
      </View>
      {isQnA ? <QnaFields /> : <TroubleshootingFields />}
      <View>
        <ToggleSwitch
          label="Make anonymous"
          enabled={isAnonymous}
          onToggle={handleAnonymousToggle}
        />
      </View>
    </View>
  );
};

export default AskQuestionForm;
