import React, { useState } from 'react';
import { Text, View } from 'react-native';
import EntitySearch from '@/src/components/EntitySearch';
import RadioButton from '@/src/components/RadioButton';
import ToggleSwitch from '@/src/components/Toggle';
import QnaFields from '../QnaFields';
import TroubleshootingFields from '../TroubleshootingFields';

const AskQuestionForm = () => {
  const [visibility, setVisibility] = React.useState('qna');
  const [toggle, setToggle] = useState(false);
  const isQnA = visibility === 'qna';

  

  return (
    <View className="mt-6 flex-1 gap-3">
      <Text className="text-xl font-medium">Ask Question</Text>
      <View>
        <Text className="text-sm leading-4 my-3">Post type</Text>
        <View className="flex-row justify-between">
          <View className="flex-1">
            <RadioButton
              selected={visibility === 'qna'}
              onPress={() => setVisibility('qna')}
              label="QnA"
            />
          </View>
          <View className="flex-1">
            <RadioButton
              selected={visibility === 'troubleshooting'}
              onPress={() => setVisibility('troubleshooting')}
              label="Troubleshooting"
            />
          </View>
        </View>
      </View>
      {isQnA ? <QnaFields /> : <TroubleshootingFields />}
      <View>
        <ToggleSwitch label="Make anonymous" enabled={toggle} onToggle={() => setToggle(!toggle)} />
      </View>
    </View>
  );
};

export default AskQuestionForm;
