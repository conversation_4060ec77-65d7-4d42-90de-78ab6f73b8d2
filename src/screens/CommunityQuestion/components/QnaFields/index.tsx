import { useSelector } from 'react-redux';
import EntitySearch from '@/src/components/EntitySearch';
import { selectMultipleSelectionsByKey, selectSelectionByKey } from '@/src/redux/selectors/search';

const QnaFields = () => {
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const topicsSelection = useSelector(selectMultipleSelectionsByKey('topic'));
  const topics = topicsSelection?.length > 1 ? topicsSelection?.map((topic) => topic.name)?.join(", ") : topicsSelection?.[0]?.name

  return (
    <>
      <EntitySearch
        title="Select topics"
        placeholder="Add a topic"
        selectionKey="topic"
        multipleSelection={true}
        className="mt-1"
        data={topics}
      />

      <EntitySearch
        title={'Department Type'}
        placeholder={`Enter department type`}
        selectionKey="department"
        data={departmentSelection ? departmentSelection.name : ''}
      />
    </>
  );
};

export default QnaFields;
