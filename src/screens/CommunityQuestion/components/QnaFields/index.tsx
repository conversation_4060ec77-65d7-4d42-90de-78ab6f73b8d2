import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import EntitySearch from '@/src/components/EntitySearch';
import ChipInput from '@/src/components/ChipInput';
import { selectMultipleSelectionsByKey, selectSelectionByKey } from '@/src/redux/selectors/search';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

const QnaFields = () => {
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const topicsSelection = useSelector(selectMultipleSelectionsByKey('topic')) as unknown as SearchResultI[];
  const [localTopics, setLocalTopics] = useState<SearchResultI[]>([]);

  useEffect(() => {
    if (!topicsSelection) return;

    setLocalTopics((prev) => {
      const existingIds = new Set(prev.map((t) => t.id));
      const merged = [...prev, ...topicsSelection.filter((t) => !existingIds.has(t.id))];
      return merged;
    });
  }, [topicsSelection]);

  return (
    <>
      <ChipInput
        title="Topics"
        placeholder="Add a topic"
        chips={localTopics}
        onRemove={(id) => setLocalTopics((prev) => prev.filter((t) => t.id !== id))}
      />

      <EntitySearch
        title={'Department Type'}
        placeholder={`Enter department type`}
        selectionKey="department"
        data={departmentSelection ? departmentSelection.name : ''}
      />
    </>
  );
};

export default QnaFields;
