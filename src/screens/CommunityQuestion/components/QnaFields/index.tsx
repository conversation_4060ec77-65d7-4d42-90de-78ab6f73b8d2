import { useSelector } from 'react-redux';
import EntitySearch from '@/src/components/EntitySearch';
import { selectSelectionByKey } from '@/src/redux/selectors/search';

const QnaFields = () => {
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  return (
    <>
      <EntitySearch
        title="Select topics"
        placeholder="Add a type"
        selectionKey="fuelType"
        multipleSelection={true}
        className="mt-1"
      />

      <EntitySearch
        title={'Department Type'}
        placeholder={`Enter department type`}
        selectionKey="department"
        data={departmentSelection ? departmentSelection.name : ''}
      />
    </>
  );
};

export default QnaFields;
