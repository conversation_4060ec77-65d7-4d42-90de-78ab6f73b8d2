import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import EntitySearch from '@/src/components/EntitySearch';
import ChipInput from '@/src/components/ChipInput';
import { selectMultipleSelectionsByKey, selectSelectionByKey } from '@/src/redux/selectors/search';
import { setMultipleSelections } from '@/src/redux/slices/entitysearch/searchSlice';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { AppDispatch } from '@/src/redux/store';

const QnaFields = () => {
  const dispatch = useDispatch<AppDispatch>();
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const topicsSelection = useSelector(selectMultipleSelectionsByKey('topic')) as unknown as SearchResultI[];
  const [localTopics, setLocalTopics] = useState<SearchResultI[]>([]);

  useEffect(() => {
    if (!topicsSelection) return;

    setLocalTopics((prev) => {
      const existingIds = new Set(prev.map((t) => t.id));
      const merged = [...prev, ...topicsSelection.filter((t) => !existingIds.has(t.id))];
      return merged;
    });
  }, [topicsSelection]);

  const handleTopicRemove = (id: string | number) => {
    const idString = id.toString();
    const updatedTopics = localTopics.filter((t) => t.id !== idString);
    setLocalTopics(updatedTopics);
    dispatch(setMultipleSelections({ key: 'topic', value: updatedTopics }));
  };

  return (
    <>
      <ChipInput
        title="Topics"
        placeholder="Add a topic"
        chips={localTopics}
        onRemove={handleTopicRemove}
      />

      <EntitySearch
        title={'Department Type'}
        placeholder={`Enter department type`}
        selectionKey="department"
        data={departmentSelection ? departmentSelection.name : ''}
      />
    </>
  );
};

export default QnaFields;
