import { View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import SafeArea from '@/src/components/SafeArea';
import CreateCommunityHeader from '../CreateCommunity/components/CreateCommunityHeader';
import AskQuestionForm from './components/AskQuestionForm';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';

const CommunityQuestionScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const handleNext = () => {
    navigation.navigate("CreateQuestion")
  }

  return (
    <SafeArea>
      <ScrollView>
        <View className="flex-1 px-5 bg-white">
          <CreateCommunityHeader currentPage={1} onNext={handleNext} />
          <AskQuestionForm />
        </View>
      </ScrollView>
    </SafeArea>
  );
};

export default CommunityQuestionScreen;
