import { View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import CreateCommunityHeader from '../CreateCommunity/components/CreateCommunityHeader';
import AskQuestionForm from './components/AskQuestionForm';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import { AppDispatch } from '@/src/redux/store';
import {
  setTopics,
  setEquipmentCategory,
  setEquipmentModel,
  setEquipmentManufacturer,
  setDepartment
} from '@/src/redux/slices/question/questionSlice';
import {
  selectSelectionByKey,
  selectMultipleSelectionsByKey
} from '@/src/redux/selectors/search';
import { selectQuestionType } from '@/src/redux/selectors/question';

const CommunityQuestionScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch<AppDispatch>();

  const topicsSelection = useSelector(selectMultipleSelectionsByKey('topic'));
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const equipmentCategorySelection = useSelector(selectSelectionByKey('equipmentCategory'));
  const equipmentManufacturerSelection = useSelector(selectSelectionByKey('equipmentManufacturer'));
  const equipmentModelSelection = useSelector(selectSelectionByKey('equipmentModel'));
  const questionType = useSelector(selectQuestionType);

  const handleNext = () => {
    if (topicsSelection) {
      dispatch(setTopics(topicsSelection));
    }

    if (departmentSelection) {
      dispatch(setDepartment(departmentSelection));
    }

    if (questionType === 'TROUBLESHOOT') {
      if (equipmentCategorySelection) {
        dispatch(setEquipmentCategory(equipmentCategorySelection));
      }
      if (equipmentManufacturerSelection) {
        dispatch(setEquipmentManufacturer(equipmentManufacturerSelection));
      }
      if (equipmentModelSelection) {
        dispatch(setEquipmentModel(equipmentModelSelection));
      }
    }
    navigation.navigate("CreateQuestion");
  };

  return (
    <SafeArea>
      <ScrollView>
        <View className="flex-1 px-5 bg-white">
          <CreateCommunityHeader currentPage={1} onNext={handleNext} />
          <AskQuestionForm />
        </View>
      </ScrollView>
    </SafeArea>
  );
};

export default CommunityQuestionScreen;
