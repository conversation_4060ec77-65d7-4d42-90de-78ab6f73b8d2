/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useRef, useState, useCallback } from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { type RouteProp, useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import type { UserI } from '@/src/redux/slices/content/types';
import type { BottomTabNavigationI, HomeStackParamListI } from '@/src/navigation/types';
import CommentInput from './components/CommentInput';
import type { CommentTypeI } from './components/CommentItem/types';
import CommentsList from './components/CommentsList';
import type { CommentsListRef } from './components/CommentsList/types';

const CommentScreen = () => {
  const [replyPreview, setReplyPreview] = useState<CommentTypeI | null>(null);

  const route = useRoute<RouteProp<HomeStackParamListI, 'Comment'>>();
  const navigation = useNavigation<BottomTabNavigationI>();
  const currentUser = useSelector(selectCurrentUser);
  const commentsListRef = useRef<CommentsListRef>(null);

  const { postId, type, portUnLocode } = route.params;

  useFocusEffect(
    useCallback(() => {
      navigation.getParent()?.setOptions({
        tabBarStyle: { display: 'none' },
      });

      return () => {
        navigation.getParent()?.setOptions({
          tabBarStyle: {
            backgroundColor: '#fff',
            height: 85,
            borderTopWidth: 0,
            paddingHorizontal: 15,
            paddingBottom: 20,
            paddingTop: 20,
            shadowColor: '#333333',
            shadowOffset: {
              width: 0,
              height: -2,
            },
            shadowOpacity: 0.08,
            shadowRadius: 8,
            elevation: 10,
          },
        });
      };
    }, [navigation]),
  );

  const handleReplyPress = (comment: CommentTypeI) => {
    setReplyPreview(comment);
  };

  const onCommentSuccess = () => {
    setReplyPreview(null);
    commentsListRef.current?.scrollToTop();
  };

  const handleBack = () => {
    Keyboard.dismiss();
    setTimeout(() => {
      navigation.goBack();
    }, 100);
  };

  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          className="flex-1"
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 20}
        >
          <View className="flex-1">
            <View className="flex-row items-center px-4">
              <BackButton onBack={handleBack} label="" />
            </View>
            <CommentsList
              ref={commentsListRef}
              postId={postId}
              onReplyPress={handleReplyPress}
              portUnLocode={portUnLocode}
              type={type ?? 'USER_POST'}
            />
            {currentUser && (
              <CommentInput
                user={currentUser as UserI}
                postId={postId}
                parentCommentId={replyPreview?.id}
                replyPreview={replyPreview as CommentTypeI}
                setReplyPreview={setReplyPreview}
                onCommentSuccess={onCommentSuccess}
                portUnLocode={portUnLocode}
                type={type ?? 'USER_POST'}
              />
            )}
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default CommentScreen;
