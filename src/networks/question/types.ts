export interface QuestionAttachmentI {
  fileUrl: string;
  filename: string;
  mimeType: string;
}

export interface CreateQuestionPayloadI {
  title: string;
  description: string;
  attachments?: QuestionAttachmentI[];
}

export interface QuestionCreateResponseI {
  id: string;
  title: string;
  description: string;
  attachments?: QuestionAttachmentI[];
  createdAt: string;
}

export interface QuestionI {
  id: string;
  title: string;
  description: string;
  attachments?: QuestionAttachmentI[];
  createdAt: string;
  updatedAt: string;
  authorId: string;
}
