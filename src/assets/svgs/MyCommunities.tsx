/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path, Rect } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const MyCommunities: React.FC<Omit<OutlinedIconPropsI, 'onPress'>> = ({
    width = 3.96,
    height = 3.96,
    color = '#448600',
    fill,
    stroke = '#D4D4D4',
    strokeWidth = 1,
    accessibilityLabel = 'Explore',
    disabled,
    ...props
}) => {
    const fillColor = fill || color;
    const opacity = disabled ? 0.5 : 1;

    return (
        <Svg
            width={RFPercentage(width)}
            height={RFPercentage(height)}
            viewBox="0 0 32 32"
            fill="none"
            opacity={opacity}
            accessibilityLabel={accessibilityLabel}
            {...props}
        >
            <Rect
                x="0.5"
                y="0.5"
                width={RFPercentage(3.8)}
                height={RFPercentage(3.8)}
                rx="7.5"
                stroke={stroke}
                strokeWidth={strokeWidth}
            />
            <Path
                d="M22.2 10.162a.753.753 0 01-1.05-.15 4.838 4.838 0 00-3.9-1.95.75.75 0 010-1.5 2.25 2.25 0 10-2.178-2.813.75.75 0 11-1.454-.375 3.75 3.75 0 116.164 3.704 6.37 6.37 0 012.571 2.033.75.75 0 01-.153 1.05zm-5.051 5.775a.75.75 0 11-1.298.75 5.344 5.344 0 00-9.202 0 .75.75 0 11-1.297-.75 6.755 6.755 0 013.163-2.805 4.5 4.5 0 115.47 0 6.755 6.755 0 013.164 2.805zm-5.899-3.375a3 3 0 100-6.001 3 3 0 000 6zM6 7.312a.75.75 0 00-.75-.75A2.25 2.25 0 117.43 3.749a.75.75 0 101.453-.375A3.75 3.75 0 102.72 7.078 6.37 6.37 0 00.15 9.111a.75.75 0 001.2.9 4.837 4.837 0 013.9-1.95.75.75 0 00.75-.75z"
                fill={fillColor}
                transform="translate(5,6)"
            />
        </Svg>
    );
};

export default MyCommunities;
