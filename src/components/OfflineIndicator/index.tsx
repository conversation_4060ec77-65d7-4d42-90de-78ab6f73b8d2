import React, { useEffect, useState, useRef } from 'react';
import { View, Text, Pressable, Platform } from 'react-native';
import NetInfo, { addEventListener } from '@react-native-community/netinfo';
import type { OfflineIndicatorProps } from './types';

const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({ children }) => {
  const [showOffline, setShowOffline] = useState(false);
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const initialCheckRef = useRef(true);

  useEffect(() => {
    NetInfo.fetch().then((state) => {
      const connected = state.isConnected && state.isInternetReachable !== false;
      setIsConnected(connected);
      initialCheckRef.current = false;
    });

    const unsubscribe = addEventListener((state) => {
      const connected = state.isConnected && state.isInternetReachable !== false;

      if (initialCheckRef.current) {
        setIsConnected(connected);
        initialCheckRef.current = false;
        return;
      }

      setIsConnected(connected);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      if (!isConnected) {
        timeoutRef.current = setTimeout(() => {
          NetInfo.fetch().then((currentState) => {
            const stillDisconnected =
              !currentState.isConnected || currentState.isInternetReachable === false;
            if (stillDisconnected) {
              setShowOffline(true);
            }
          });
        }, 5000);
      } else {
        setShowOffline(false);
        if (retryTimeoutRef.current) {
          clearTimeout(retryTimeoutRef.current);
          retryTimeoutRef.current = null;
        }
      }
    });

    return () => {
      unsubscribe();
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  const handleRetry = async () => {
    setShowOffline(false);

    try {
      const state = await NetInfo.fetch();
      const connected = state.isConnected && state.isInternetReachable !== false;

      if (!connected) {
        retryTimeoutRef.current = setTimeout(() => {
          setShowOffline(true);
        }, 3000);
      }
    } catch (error) {
      retryTimeoutRef.current = setTimeout(() => {
        setShowOffline(true);
      }, 3000);
    }
  };

  if (!showOffline) {
    return <>{children}</>;
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View
        style={{
          position: 'absolute',
          top: Platform.OS === 'ios' ? 50 : 30,
          left: 0,
          right: 0,
          zIndex: 1000,
        }}
        className="mx-4 bg-red-500 rounded-xl shadow-lg mt-10"
      >
        <View className="px-4 py-3 flex-row items-center justify-between">
          <View className="flex-row items-center flex-1">
            <View className="w-2 h-2 bg-white rounded-full mr-3" />
            <Text className="text-white font-medium text-sm">No internet connection</Text>
          </View>
          <Pressable
            onPress={handleRetry}
            className="bg-white/20 px-3 py-1 rounded-lg active:bg-white/30"
          >
            <Text className="text-white text-xs font-medium">Retry</Text>
          </Pressable>
        </View>
      </View>

      <View className="flex-1 items-center justify-center px-8">
        <View className="w-32 h-32 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full items-center justify-center mb-8 shadow-sm">
          <View className="w-20 h-20 bg-white rounded-full items-center justify-center">
            <Text className="text-4xl">📡</Text>
          </View>
        </View>

        <View className="items-center mb-8">
          <Text className="text-2xl font-bold text-gray-900 text-center mb-3">You're offline</Text>
          <Text className="text-base text-gray-600 text-center leading-6 max-w-sm">
            Check your internet connection and try again. Some features may not be available.
          </Text>
        </View>

        <Pressable
          onPress={handleRetry}
          className="bg-green-600 rounded-xl py-4 px-8 shadow-lg active:bg-green-700 mb-4"
        >
          <Text className="text-white font-semibold text-lg">Try Again</Text>
        </Pressable>

        <View className="bg-green-50 rounded-xl p-4 border border-green-100">
          <Text className="text-green-800 font-medium text-center mb-2">Troubleshooting tips:</Text>
          <View className="space-y-1">
            <Text className="text-green-700 text-sm text-center">
              • Check your WiFi or mobile data
            </Text>
            <Text className="text-green-700 text-sm text-center">
              • Move to an area with better signal
            </Text>
            <Text className="text-green-700 text-sm text-center">
              • Restart your device if needed
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default OfflineIndicator;
